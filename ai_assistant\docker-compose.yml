# AI Coding Assistant - Docker Compose Configuration
# Complete production-ready setup with monitoring and scaling

version: '3.8'

services:
  # Main AI Assistant Application
  ai-assistant:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: ai-assistant-app
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - FLASK_DEBUG=False
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - SECRET_KEY=${SECRET_KEY}
      - MAX_REQUESTS_PER_MINUTE=60
      - MAX_TOKENS_PER_REQUEST=4000
      - SESSION_TIMEOUT_MINUTES=120
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    networks:
      - ai-assistant-network
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.ai-assistant.rule=Host(`ai-assistant.local`)"
      - "traefik.http.services.ai-assistant.loadbalancer.server.port=5000"

  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    container_name: ai-assistant-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - ai-assistant-network
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy and load balancer
  nginx:
    image: nginx:alpine
    container_name: ai-assistant-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx-cache:/var/cache/nginx
    networks:
      - ai-assistant-network
    depends_on:
      - ai-assistant
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: ai-assistant-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    networks:
      - ai-assistant-network
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: ai-assistant-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - ai-assistant-network
    depends_on:
      - prometheus

  # Log aggregation with Loki
  loki:
    image: grafana/loki:latest
    container_name: ai-assistant-loki
    restart: unless-stopped
    ports:
      - "3100:3100"
    volumes:
      - ./loki-config.yml:/etc/loki/local-config.yaml:ro
      - loki-data:/loki
    networks:
      - ai-assistant-network
    command: -config.file=/etc/loki/local-config.yaml

  # Log shipping with Promtail
  promtail:
    image: grafana/promtail:latest
    container_name: ai-assistant-promtail
    restart: unless-stopped
    volumes:
      - ./promtail-config.yml:/etc/promtail/config.yml:ro
      - ./logs:/var/log/ai-assistant:ro
      - /var/log:/var/log:ro
    networks:
      - ai-assistant-network
    depends_on:
      - loki
    command: -config.file=/etc/promtail/config.yml

  # Database for production (optional)
  postgres:
    image: postgres:15-alpine
    container_name: ai-assistant-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=ai_assistant
      - POSTGRES_USER=ai_assistant
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - ai-assistant-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ai_assistant"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backup service
  backup:
    image: alpine:latest
    container_name: ai-assistant-backup
    restart: "no"
    volumes:
      - ./data:/backup/data:ro
      - ./logs:/backup/logs:ro
      - postgres-data:/backup/postgres:ro
      - ./backups:/backups
    networks:
      - ai-assistant-network
    command: |
      sh -c "
        apk add --no-cache tar gzip postgresql-client &&
        while true; do
          echo 'Creating backup...' &&
          tar -czf /backups/ai-assistant-backup-$$(date +%Y%m%d-%H%M%S).tar.gz /backup &&
          echo 'Backup completed' &&
          sleep 86400
        done
      "

networks:
  ai-assistant-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  redis-data:
    driver: local
  nginx-cache:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  loki-data:
    driver: local
  postgres-data:
    driver: local

# Environment variables template
# Create a .env file with these variables:
#
# OPENROUTER_API_KEY=your_openrouter_api_key_here
# SECRET_KEY=your_super_secret_key_here
# POSTGRES_PASSWORD=your_postgres_password_here
#
# Usage:
# docker-compose up -d                    # Start all services
# docker-compose up ai-assistant          # Start only the main app
# docker-compose logs -f ai-assistant     # View logs
# docker-compose down                     # Stop all services
# docker-compose down -v                  # Stop and remove volumes
