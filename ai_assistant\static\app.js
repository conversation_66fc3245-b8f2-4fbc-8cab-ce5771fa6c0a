/**
 * Advanced AI Coding Assistant - Client Application
 * Modern JavaScript with comprehensive error handling, accessibility, and performance optimizations
 */

class AIAssistantApp {
    constructor() {
        // Configuration from HTML
        this.config = this.loadConfig();
        
        // DOM elements
        this.elements = this.initializeElements();
        
        // Application state
        this.state = {
            isLoading: false,
            currentSessionId: this.config.sessionId,
            messageHistory: [],
            settings: this.loadSettings(),
            retryCount: 0,
            maxRetries: 3
        };
        
        // Initialize the application
        this.init();
    }
    
    loadConfig() {
        try {
            const configElement = document.getElementById('app-config');
            return configElement ? JSON.parse(configElement.textContent) : {};
        } catch (error) {
            console.error('Failed to load app config:', error);
            return {};
        }
    }
    
    initializeElements() {
        const elements = {
            // Form elements
            questionForm: document.getElementById('question-form'),
            questionInput: document.getElementById('question-input'),
            askButton: document.getElementById('ask-button'),
            clearButton: document.getElementById('clear-button'),
            
            // Display elements
            chatHistory: document.getElementById('chat-history'),
            welcomeMessage: document.getElementById('welcome-message'),
            loadingOverlay: document.getElementById('loading-overlay'),
            loadingText: document.getElementById('loading-text'),
            progressBar: document.getElementById('progress-bar'),
            
            // Status elements
            statusIndicator: document.getElementById('status-indicator'),
            statusDot: document.getElementById('status-dot'),
            statusText: document.getElementById('status-text'),
            charCounter: document.getElementById('char-counter'),
            
            // Modal elements
            settingsModal: document.getElementById('settings-modal'),
            settingsButton: document.getElementById('settings-button'),
            modalClose: document.getElementById('modal-close'),
            modalOverlay: document.getElementById('modal-overlay'),
            
            // Settings elements
            themeSelect: document.getElementById('theme-select'),
            fontSizeSelect: document.getElementById('font-size-select'),
            soundEnabled: document.getElementById('sound-enabled'),
            autoScroll: document.getElementById('auto-scroll'),
            
            // Other elements
            exportButton: document.getElementById('export-button'),
            toastContainer: document.getElementById('toast-container')
        };
        
        // Validate critical elements
        const criticalElements = ['questionForm', 'questionInput', 'askButton', 'chatHistory'];
        for (const elementName of criticalElements) {
            if (!elements[elementName]) {
                throw new Error(`Critical element not found: ${elementName}`);
            }
        }
        
        return elements;
    }
    
    init() {
        try {
            this.setupEventListeners();
            this.setupKeyboardShortcuts();
            this.applySettings();
            this.updateStatus('ready', 'Ready');
            this.loadConversationHistory();
            
            // Focus the input for better UX
            this.elements.questionInput.focus();
            
            console.log('AI Assistant App initialized successfully');
        } catch (error) {
            console.error('Failed to initialize app:', error);
            this.showToast('error', 'Initialization Error', 'Failed to initialize the application');
        }
    }
    
    setupEventListeners() {
        // Form submission
        this.elements.questionForm.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleQuestionSubmit();
        });
        
        // Input character counter
        this.elements.questionInput.addEventListener('input', () => {
            this.updateCharacterCounter();
        });
        
        // Clear conversation
        this.elements.clearButton?.addEventListener('click', () => {
            this.clearConversation();
        });
        
        // Settings modal
        this.elements.settingsButton?.addEventListener('click', () => {
            this.openSettingsModal();
        });
        
        this.elements.modalClose?.addEventListener('click', () => {
            this.closeSettingsModal();
        });
        
        this.elements.modalOverlay?.addEventListener('click', () => {
            this.closeSettingsModal();
        });
        
        // Settings changes
        this.elements.themeSelect?.addEventListener('change', (e) => {
            this.updateTheme(e.target.value);
        });
        
        this.elements.fontSizeSelect?.addEventListener('change', (e) => {
            this.updateFontSize(e.target.value);
        });
        
        this.elements.soundEnabled?.addEventListener('change', (e) => {
            this.updateSetting('soundEnabled', e.target.checked);
        });
        
        this.elements.autoScroll?.addEventListener('change', (e) => {
            this.updateSetting('autoScroll', e.target.checked);
        });
        
        // Export conversation
        this.elements.exportButton?.addEventListener('click', () => {
            this.exportConversation();
        });
        
        // Window events
        window.addEventListener('beforeunload', () => {
            this.saveSettings();
        });
        
        window.addEventListener('online', () => {
            this.updateStatus('ready', 'Ready');
        });
        
        window.addEventListener('offline', () => {
            this.updateStatus('error', 'Offline');
        });
    }
    
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Enter to submit
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                if (!this.state.isLoading) {
                    this.handleQuestionSubmit();
                }
            }
            
            // Escape to close modal
            if (e.key === 'Escape') {
                this.closeSettingsModal();
            }
            
            // Ctrl/Cmd + K to focus input
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                this.elements.questionInput.focus();
            }
            
            // Ctrl/Cmd + L to clear conversation
            if ((e.ctrlKey || e.metaKey) && e.key === 'l') {
                e.preventDefault();
                this.clearConversation();
            }
        });
    }
    
    async handleQuestionSubmit() {
        const question = this.elements.questionInput.value.trim();
        
        if (!question) {
            this.showToast('warning', 'Empty Question', 'Please enter a question before submitting');
            return;
        }
        
        if (question.length > this.config.maxMessageLength) {
            this.showToast('error', 'Message Too Long', `Please keep your message under ${this.config.maxMessageLength} characters`);
            return;
        }
        
        try {
            this.setLoadingState(true);
            this.hideWelcomeMessage();
            
            // Add user message to chat
            this.addMessage('user', question);
            
            // Clear input
            this.elements.questionInput.value = '';
            this.updateCharacterCounter();
            
            // Send request to API
            const response = await this.sendQuestion(question);
            
            // Handle response
            if (response.error) {
                this.handleErrorResponse(response);
            } else {
                this.handleSuccessResponse(response);
                this.state.retryCount = 0; // Reset retry count on success
            }
            
        } catch (error) {
            console.error('Error submitting question:', error);
            this.handleNetworkError(error);
        } finally {
            this.setLoadingState(false);
        }
    }
    
    async sendQuestion(question) {
        const requestData = {
            question: question,
            session_id: this.state.currentSessionId,
            context: {
                timestamp: new Date().toISOString(),
                user_agent: navigator.userAgent,
                viewport: {
                    width: window.innerWidth,
                    height: window.innerHeight
                }
            }
        };
        
        const response = await fetch(this.config.apiEndpoint || '/ask', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(requestData)
        });
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
        }
        
        return await response.json();
    }
    
    handleSuccessResponse(response) {
        const answer = response.answer;
        
        if (typeof answer === 'string') {
            // Simple string response
            this.addMessage('ai', answer);
        } else if (answer && typeof answer === 'object') {
            // Structured response
            this.addMessage('ai', answer.explanation || JSON.stringify(answer), {
                structured: true,
                data: answer
            });
        } else {
            this.addMessage('ai', 'I received your question but couldn\'t generate a proper response. Please try again.');
        }
        
        // Update session ID if provided
        if (response.session_id) {
            this.state.currentSessionId = response.session_id;
        }
        
        // Show success feedback
        if (this.state.settings.soundEnabled) {
            this.playNotificationSound('success');
        }
        
        // Auto-scroll to new message
        if (this.state.settings.autoScroll) {
            this.scrollToBottom();
        }
    }
    
    handleErrorResponse(response) {
        const errorMessage = response.message || 'An error occurred while processing your request';
        const suggestion = response.suggestion || 'Please try again or rephrase your question';
        
        this.addMessage('error', `${errorMessage}\n\n💡 ${suggestion}`);
        
        if (response.retry_after) {
            this.showToast('warning', 'Rate Limited', `Please wait ${response.retry_after} seconds before trying again`);
        }
        
        if (this.state.settings.soundEnabled) {
            this.playNotificationSound('error');
        }
    }
    
    handleNetworkError(error) {
        this.state.retryCount++;
        
        if (this.state.retryCount < this.state.maxRetries) {
            this.showToast('warning', 'Network Error', `Retrying... (${this.state.retryCount}/${this.state.maxRetries})`);
            setTimeout(() => {
                this.handleQuestionSubmit();
            }, 2000 * this.state.retryCount); // Exponential backoff
        } else {
            this.addMessage('error', `Network error: ${error.message}\n\nPlease check your connection and try again.`);
            this.showToast('error', 'Connection Failed', 'Please check your internet connection');
            this.state.retryCount = 0;
        }
    }
    
    addMessage(type, content, metadata = {}) {
        const messageId = `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const timestamp = new Date().toLocaleTimeString();
        
        const messageElement = this.createMessageElement(messageId, type, content, timestamp, metadata);
        
        this.elements.chatHistory.appendChild(messageElement);
        
        // Store in message history
        this.state.messageHistory.push({
            id: messageId,
            type,
            content,
            timestamp: new Date().toISOString(),
            metadata
        });
        
        // Auto-scroll if enabled
        if (this.state.settings.autoScroll) {
            this.scrollToBottom();
        }
        
        // Announce to screen readers
        this.announceMessage(type, content);
    }

    createMessageElement(id, type, content, timestamp, metadata) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;
        messageDiv.id = id;
        messageDiv.setAttribute('role', 'article');
        messageDiv.setAttribute('aria-label', `${type} message at ${timestamp}`);

        const headerDiv = document.createElement('div');
        headerDiv.className = 'message-header';

        const typeSpan = document.createElement('span');
        typeSpan.className = 'message-type';
        typeSpan.textContent = type === 'user' ? '👤 You' : type === 'ai' ? '🤖 AI Assistant' : '⚠️ System';

        const timeSpan = document.createElement('span');
        timeSpan.className = 'message-time';
        timeSpan.textContent = timestamp;

        headerDiv.appendChild(typeSpan);
        headerDiv.appendChild(timeSpan);

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';

        if (type === 'ai' && typeof content === 'object') {
            // Structured AI response
            this.renderStructuredContent(contentDiv, content);
        } else {
            // Plain text content
            contentDiv.innerHTML = this.formatMessageContent(content);
        }

        messageDiv.appendChild(headerDiv);
        messageDiv.appendChild(contentDiv);

        // Add animation
        messageDiv.classList.add('slide-up');

        return messageDiv;
    }

    renderStructuredContent(container, response) {
        // Main explanation
        if (response.explanation) {
            const explanationDiv = document.createElement('div');
            explanationDiv.className = 'response-explanation';
            explanationDiv.innerHTML = this.formatMessageContent(response.explanation);
            container.appendChild(explanationDiv);
        }

        // Code snippet
        if (response.code_snippet) {
            const codeDiv = document.createElement('div');
            codeDiv.className = 'code-block';
            codeDiv.setAttribute('data-language', response.language || 'text');

            const codeElement = document.createElement('code');
            codeElement.textContent = response.code_snippet;
            codeDiv.appendChild(codeElement);

            // Add copy button
            const copyButton = document.createElement('button');
            copyButton.className = 'copy-button';
            copyButton.textContent = '📋 Copy';
            copyButton.onclick = () => this.copyToClipboard(response.code_snippet, copyButton);
            codeDiv.appendChild(copyButton);

            container.appendChild(codeDiv);
        }

        // Best practices
        if (response.best_practices && response.best_practices.length > 0) {
            const practicesDiv = document.createElement('div');
            practicesDiv.className = 'best-practices';

            const title = document.createElement('h4');
            title.textContent = '💡 Best Practices';
            practicesDiv.appendChild(title);

            const list = document.createElement('ul');
            response.best_practices.forEach(practice => {
                const item = document.createElement('li');
                item.textContent = practice;
                list.appendChild(item);
            });
            practicesDiv.appendChild(list);
            container.appendChild(practicesDiv);
        }

        // Additional resources
        if (response.additional_resources && response.additional_resources.length > 0) {
            const resourcesDiv = document.createElement('div');
            resourcesDiv.className = 'additional-resources';

            const title = document.createElement('h4');
            title.textContent = '📚 Additional Resources';
            resourcesDiv.appendChild(title);

            const list = document.createElement('ul');
            response.additional_resources.forEach(resource => {
                const item = document.createElement('li');
                if (resource.startsWith('http')) {
                    const link = document.createElement('a');
                    link.href = resource;
                    link.textContent = resource;
                    link.target = '_blank';
                    link.rel = 'noopener noreferrer';
                    item.appendChild(link);
                } else {
                    item.textContent = resource;
                }
                list.appendChild(item);
            });
            resourcesDiv.appendChild(list);
            container.appendChild(resourcesDiv);
        }

        // Confidence score
        if (response.confidence_score !== undefined) {
            const confidenceDiv = document.createElement('div');
            confidenceDiv.className = 'confidence-score';

            const percentage = Math.round(response.confidence_score * 100);
            confidenceDiv.innerHTML = `
                <span class="confidence-label">Confidence:</span>
                <div class="confidence-bar">
                    <div class="confidence-fill" style="width: ${percentage}%"></div>
                </div>
                <span class="confidence-value">${percentage}%</span>
            `;
            container.appendChild(confidenceDiv);
        }

        // Follow-up suggestions
        if (response.follow_up_suggestions && response.follow_up_suggestions.length > 0) {
            const suggestionsDiv = document.createElement('div');
            suggestionsDiv.className = 'follow-up-suggestions';

            const title = document.createElement('h4');
            title.textContent = '🔍 Follow-up Questions';
            suggestionsDiv.appendChild(title);

            response.follow_up_suggestions.forEach(suggestion => {
                const button = document.createElement('button');
                button.className = 'suggestion-button';
                button.textContent = suggestion;
                button.onclick = () => {
                    this.elements.questionInput.value = suggestion;
                    this.elements.questionInput.focus();
                    this.updateCharacterCounter();
                };
                suggestionsDiv.appendChild(button);
            });

            container.appendChild(suggestionsDiv);
        }
    }

    formatMessageContent(content) {
        // Convert markdown-like formatting to HTML
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>')
            .replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
                return `<div class="code-block" data-language="${lang || 'text'}"><code>${code.trim()}</code></div>`;
            });
    }

    async copyToClipboard(text, button) {
        try {
            await navigator.clipboard.writeText(text);
            const originalText = button.textContent;
            button.textContent = '✅ Copied!';
            button.classList.add('success');

            setTimeout(() => {
                button.textContent = originalText;
                button.classList.remove('success');
            }, 2000);

            this.showToast('success', 'Copied!', 'Code copied to clipboard');
        } catch (error) {
            console.error('Failed to copy to clipboard:', error);
            this.showToast('error', 'Copy Failed', 'Could not copy to clipboard');
        }
    }

    announceMessage(type, content) {
        // Create announcement for screen readers
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'polite');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.className = 'sr-only';

        const messageType = type === 'user' ? 'Your message' : type === 'ai' ? 'AI response' : 'System message';
        const contentPreview = typeof content === 'string' ? content.substring(0, 100) : 'Response received';

        announcement.textContent = `${messageType}: ${contentPreview}`;

        document.body.appendChild(announcement);

        // Remove after announcement
        setTimeout(() => {
            document.body.removeChild(announcement);
        }, 1000);
    }

    updateCharacterCounter() {
        const length = this.elements.questionInput.value.length;
        const maxLength = this.config.maxMessageLength || 5000;

        this.elements.charCounter.textContent = `${length} / ${maxLength}`;

        // Update styling based on length
        this.elements.charCounter.classList.remove('warning', 'error');
        if (length > maxLength * 0.9) {
            this.elements.charCounter.classList.add('error');
        } else if (length > maxLength * 0.8) {
            this.elements.charCounter.classList.add('warning');
        }
    }

    setLoadingState(isLoading) {
        this.state.isLoading = isLoading;

        // Update UI elements
        this.elements.askButton.disabled = isLoading;
        this.elements.questionInput.disabled = isLoading;

        if (isLoading) {
            this.elements.askButton.querySelector('.button-text').textContent = 'Thinking...';
            this.elements.loadingOverlay.classList.add('visible');
            this.elements.loadingOverlay.setAttribute('aria-hidden', 'false');
            this.updateStatus('loading', 'Processing...');
            this.startLoadingAnimation();
        } else {
            this.elements.askButton.querySelector('.button-text').textContent = 'Ask AI';
            this.elements.loadingOverlay.classList.remove('visible');
            this.elements.loadingOverlay.setAttribute('aria-hidden', 'true');
            this.updateStatus('ready', 'Ready');
            this.stopLoadingAnimation();
        }
    }

    startLoadingAnimation() {
        const messages = [
            'Analyzing your question...',
            'Consulting AI models...',
            'Generating response...',
            'Applying best practices...',
            'Finalizing answer...'
        ];

        let messageIndex = 0;
        this.loadingInterval = setInterval(() => {
            this.elements.loadingText.textContent = messages[messageIndex];
            messageIndex = (messageIndex + 1) % messages.length;
        }, 1500);
    }

    stopLoadingAnimation() {
        if (this.loadingInterval) {
            clearInterval(this.loadingInterval);
            this.loadingInterval = null;
        }
    }

    updateStatus(type, text) {
        this.elements.statusText.textContent = text;
        this.elements.statusDot.className = `status-dot ${type}`;
    }

    hideWelcomeMessage() {
        if (this.elements.welcomeMessage) {
            this.elements.welcomeMessage.style.display = 'none';
        }
    }

    scrollToBottom() {
        this.elements.chatHistory.scrollTop = this.elements.chatHistory.scrollHeight;
    }

    clearConversation() {
        if (confirm('Are you sure you want to clear the conversation? This action cannot be undone.')) {
            this.elements.chatHistory.innerHTML = '';
            this.state.messageHistory = [];
            this.elements.welcomeMessage.style.display = 'block';
            this.showToast('success', 'Cleared', 'Conversation history cleared');
        }
    }

    // Settings Management
    loadSettings() {
        const defaultSettings = {
            theme: 'auto',
            fontSize: 'medium',
            soundEnabled: false,
            autoScroll: true
        };

        try {
            const saved = localStorage.getItem('ai-assistant-settings');
            return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
        } catch (error) {
            console.error('Failed to load settings:', error);
            return defaultSettings;
        }
    }

    saveSettings() {
        try {
            localStorage.setItem('ai-assistant-settings', JSON.stringify(this.state.settings));
        } catch (error) {
            console.error('Failed to save settings:', error);
        }
    }

    updateSetting(key, value) {
        this.state.settings[key] = value;
        this.saveSettings();
    }

    applySettings() {
        // Apply theme
        this.updateTheme(this.state.settings.theme);

        // Apply font size
        this.updateFontSize(this.state.settings.fontSize);

        // Update form controls
        if (this.elements.themeSelect) {
            this.elements.themeSelect.value = this.state.settings.theme;
        }
        if (this.elements.fontSizeSelect) {
            this.elements.fontSizeSelect.value = this.state.settings.fontSize;
        }
        if (this.elements.soundEnabled) {
            this.elements.soundEnabled.checked = this.state.settings.soundEnabled;
        }
        if (this.elements.autoScroll) {
            this.elements.autoScroll.checked = this.state.settings.autoScroll;
        }
    }

    updateTheme(theme) {
        this.updateSetting('theme', theme);

        // Remove existing theme classes
        document.documentElement.removeAttribute('data-theme');

        if (theme === 'dark') {
            document.documentElement.setAttribute('data-theme', 'dark');
        } else if (theme === 'light') {
            document.documentElement.setAttribute('data-theme', 'light');
        } else {
            // Auto theme - use system preference
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            if (prefersDark) {
                document.documentElement.setAttribute('data-theme', 'dark');
            }
        }
    }

    updateFontSize(size) {
        this.updateSetting('fontSize', size);

        // Remove existing font size classes
        document.body.classList.remove('font-small', 'font-large');

        if (size === 'small') {
            document.body.classList.add('font-small');
        } else if (size === 'large') {
            document.body.classList.add('font-large');
        }
    }

    // Modal Management
    openSettingsModal() {
        this.elements.settingsModal.classList.add('visible');
        this.elements.settingsModal.setAttribute('aria-hidden', 'false');

        // Focus the first input for accessibility
        const firstInput = this.elements.settingsModal.querySelector('select, input');
        if (firstInput) {
            firstInput.focus();
        }

        // Prevent body scroll
        document.body.style.overflow = 'hidden';
    }

    closeSettingsModal() {
        this.elements.settingsModal.classList.remove('visible');
        this.elements.settingsModal.setAttribute('aria-hidden', 'true');

        // Restore body scroll
        document.body.style.overflow = '';

        // Return focus to settings button
        if (this.elements.settingsButton) {
            this.elements.settingsButton.focus();
        }
    }

    // Toast Notifications
    showToast(type, title, message, duration = 5000) {
        const toastId = `toast-${Date.now()}`;
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.id = toastId;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');

        const icon = this.getToastIcon(type);

        toast.innerHTML = `
            <div class="toast-icon">${icon}</div>
            <div class="toast-content">
                <div class="toast-title">${title}</div>
                <div class="toast-message">${message}</div>
            </div>
            <button class="toast-close" aria-label="Close notification">×</button>
            <div class="toast-progress"></div>
        `;

        // Add close functionality
        const closeButton = toast.querySelector('.toast-close');
        closeButton.addEventListener('click', () => {
            this.removeToast(toastId);
        });

        // Add to container
        this.elements.toastContainer.appendChild(toast);

        // Auto-remove after duration
        setTimeout(() => {
            this.removeToast(toastId);
        }, duration);

        // Play sound if enabled
        if (this.state.settings.soundEnabled) {
            this.playNotificationSound(type);
        }
    }

    getToastIcon(type) {
        const icons = {
            success: '✅',
            warning: '⚠️',
            error: '❌',
            info: 'ℹ️'
        };
        return icons[type] || icons.info;
    }

    removeToast(toastId) {
        const toast = document.getElementById(toastId);
        if (toast) {
            toast.style.animation = 'toastSlideOut 0.3s ease-out forwards';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }

    // Audio Notifications
    playNotificationSound(type) {
        if (!this.state.settings.soundEnabled) return;

        try {
            // Create audio context for web audio API
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // Different frequencies for different notification types
            const frequencies = {
                success: 800,
                warning: 600,
                error: 400,
                info: 500
            };

            oscillator.frequency.setValueAtTime(frequencies[type] || frequencies.info, audioContext.currentTime);
            oscillator.type = 'sine';

            // Fade in and out
            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.1);
            gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.3);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);

        } catch (error) {
            console.warn('Could not play notification sound:', error);
        }
    }

    // Conversation History
    async loadConversationHistory() {
        try {
            const response = await fetch(this.config.historyEndpoint || '/history');
            if (response.ok) {
                const data = await response.json();
                if (data.history && data.history.length > 0) {
                    this.hideWelcomeMessage();
                    data.history.reverse().forEach(entry => {
                        if (!entry.error) {
                            this.addMessage('user', entry.question, { timestamp: entry.timestamp });
                            this.addMessage('ai', entry.answer, { timestamp: entry.timestamp });
                        }
                    });
                }
            }
        } catch (error) {
            console.warn('Could not load conversation history:', error);
        }
    }

    // Export Functionality
    exportConversation() {
        const data = {
            sessionId: this.state.currentSessionId,
            timestamp: new Date().toISOString(),
            messages: this.state.messageHistory,
            settings: this.state.settings
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `ai-conversation-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        URL.revokeObjectURL(url);

        this.showToast('success', 'Exported', 'Conversation exported successfully');
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.aiAssistant = new AIAssistantApp();
    } catch (error) {
        console.error('Failed to initialize AI Assistant:', error);

        // Show fallback error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.innerHTML = `
            <h2>⚠️ Initialization Error</h2>
            <p>The AI Assistant failed to initialize properly. Please refresh the page and try again.</p>
            <p><small>Error: ${error.message}</small></p>
        `;
        errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 2px solid #ef4444;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            z-index: 9999;
            text-align: center;
            max-width: 400px;
        `;

        document.body.appendChild(errorDiv);
    }
});

// Add CSS for toast slide out animation
const style = document.createElement('style');
style.textContent = `
    @keyframes toastSlideOut {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(100%);
        }
    }

    .copy-button {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        cursor: pointer;
        transition: all 0.2s;
    }

    .copy-button:hover {
        background: rgba(255, 255, 255, 0.2);
    }

    .copy-button.success {
        background: #10b981;
        border-color: #10b981;
    }

    .confidence-score {
        margin-top: 1rem;
        padding: 0.75rem;
        background: var(--bg-secondary);
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
    }

    .confidence-bar {
        flex: 1;
        height: 0.5rem;
        background: var(--gray-200);
        border-radius: 0.25rem;
        overflow: hidden;
    }

    .confidence-fill {
        height: 100%;
        background: linear-gradient(90deg, #ef4444 0%, #f59e0b 50%, #10b981 100%);
        transition: width 0.3s ease;
    }

    .suggestion-button {
        display: inline-block;
        margin: 0.25rem 0.5rem 0.25rem 0;
        padding: 0.5rem 0.75rem;
        background: var(--primary-50);
        border: 1px solid var(--primary-200);
        border-radius: 1rem;
        color: var(--primary-700);
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s;
    }

    .suggestion-button:hover {
        background: var(--primary-100);
        border-color: var(--primary-300);
    }

    .best-practices,
    .additional-resources,
    .follow-up-suggestions {
        margin-top: 1rem;
        padding: 0.75rem;
        background: var(--bg-secondary);
        border-radius: 0.5rem;
    }

    .best-practices h4,
    .additional-resources h4,
    .follow-up-suggestions h4 {
        margin: 0 0 0.5rem 0;
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-primary);
    }

    .best-practices ul,
    .additional-resources ul {
        margin: 0;
        padding-left: 1.25rem;
    }

    .best-practices li,
    .additional-resources li {
        margin-bottom: 0.25rem;
        font-size: 0.875rem;
        line-height: 1.4;
    }
`;
document.head.appendChild(style);
