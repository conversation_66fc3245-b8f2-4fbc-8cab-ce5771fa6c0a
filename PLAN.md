# AI Coding Assistant - Minimal Flask Web App

## Project Overview
A very minimal AI coding assistant web app that runs entirely locally using Flask, Pydantic, and vanilla HTML/CSS/JS. It uses the OpenRouter API to send user questions to a Large Language Model and stores conversation history locally.

## Folder Structure
```
ai_assistant/
├── app.py
├── agents.py
├── templates/
│   └── index.html
├── static/
│   ├── style.css
│   └── app.js
├── data/
│   └── history.json
└── .env
```

## File Contents

### app.py
```python
import os
import json
from datetime import datetime
from flask import Flask, render_template, request, jsonify
from pydantic import BaseModel, ValidationError
from dotenv import load_dotenv
import requests
from agents import AIAgent

# Load environment variables
load_dotenv()

app = Flask(__name__)

# Pydantic models for validation
class QuestionRequest(BaseModel):
    question: str

class AnswerResponse(BaseModel):
    answer: str

# Initialize AI agent
ai_agent = AIAgent(api_key=os.getenv('OPENROUTER_API_KEY'))

@app.route('/')
def index():
    """Serve the main HTML page"""
    return render_template('index.html')

@app.route('/ask', methods=['POST'])
def ask():
    """Handle AI questions and return responses"""
    try:
        # Validate incoming JSON
        data = request.get_json()
        question_req = QuestionRequest(**data)
        
        # Get AI response
        answer = ai_agent.get_response(question_req.question)
        
        # Save to history
        save_to_history(question_req.question, answer)
        
        # Return validated response
        response = AnswerResponse(answer=answer)
        return jsonify(response.dict())
        
    except ValidationError as e:
        return jsonify({"error": "Invalid request format", "details": str(e)}), 400
    except Exception as e:
        return jsonify({"error": "Internal server error", "details": str(e)}), 500

def save_to_history(question, answer):
    """Save Q&A to local JSON file"""
    history_file = 'data/history.json'
    
    # Ensure data directory exists
    os.makedirs('data', exist_ok=True)
    
    # Load existing history
    try:
        with open(history_file, 'r', encoding='utf-8') as f:
            history = json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        history = []
    
    # Add new entry
    entry = {
        "timestamp": datetime.now().isoformat(),
        "question": question,
        "answer": answer
    }
    history.append(entry)
    
    # Save updated history
    with open(history_file, 'w', encoding='utf-8') as f:
        json.dump(history, f, indent=2, ensure_ascii=False)

if __name__ == '__main__':
    app.run(debug=True)
```

### agents.py
```python
import requests
from typing import Optional

class AIAgent:
    """Simple AI agent that interfaces with OpenRouter API"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def get_response(self, question: str, model: str = "anthropic/claude-3-haiku") -> str:
        """Get AI response from OpenRouter API"""
        payload = {
            "model": model,
            "messages": [
                {
                    "role": "system",
                    "content": "You are a helpful AI coding assistant. Provide clear, concise answers to programming questions."
                },
                {
                    "role": "user",
                    "content": question
                }
            ],
            "max_tokens": 1000,
            "temperature": 0.7
        }
        
        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            
            data = response.json()
            return data["choices"][0]["message"]["content"]
            
        except requests.exceptions.RequestException as e:
            return f"Error communicating with AI service: {str(e)}"
        except (KeyError, IndexError) as e:
            return f"Error parsing AI response: {str(e)}"
```

### templates/index.html
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Coding Assistant</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <header>
            <h1>AI Coding Assistant</h1>
            <p>Ask me anything about programming!</p>
        </header>

        <main>
            <div class="chat-container">
                <div id="chat-history" class="chat-history">
                    <!-- Chat messages will appear here -->
                </div>

                <div class="input-section">
                    <textarea
                        id="question-input"
                        placeholder="Type your coding question here..."
                        rows="3"
                    ></textarea>
                    <button id="ask-button" onclick="askQuestion()">Ask AI</button>
                </div>
            </div>

            <div id="loading" class="loading hidden">
                <div class="spinner"></div>
                <p>Thinking...</p>
            </div>
        </main>
    </div>

    <script src="{{ url_for('static', filename='app.js') }}"></script>
</body>
</html>
```

### static/style.css
```css
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
}

header p {
    color: #7f8c8d;
}

.chat-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.chat-history {
    max-height: 400px;
    overflow-y: auto;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.message {
    margin-bottom: 20px;
    padding: 15px;
    border-radius: 8px;
}

.user-message {
    background-color: #3498db;
    color: white;
    margin-left: 20%;
}

.ai-message {
    background-color: #ecf0f1;
    color: #2c3e50;
    margin-right: 20%;
}

.input-section {
    padding: 20px;
    display: flex;
    gap: 10px;
}

#question-input {
    flex: 1;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    resize: vertical;
    min-height: 60px;
}

#question-input:focus {
    outline: none;
    border-color: #3498db;
}

#ask-button {
    padding: 12px 24px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: background-color 0.3s;
}

#ask-button:hover {
    background-color: #2980b9;
}

#ask-button:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
}

.loading {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-top: 20px;
}

.hidden {
    display: none;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error {
    background-color: #e74c3c;
    color: white;
    padding: 10px;
    border-radius: 6px;
    margin: 10px 0;
}
```

### static/app.js
```javascript
// DOM elements
const questionInput = document.getElementById('question-input');
const askButton = document.getElementById('ask-button');
const chatHistory = document.getElementById('chat-history');
const loading = document.getElementById('loading');

// Event listeners
questionInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        askQuestion();
    }
});

// Main function to ask AI question
async function askQuestion() {
    const question = questionInput.value.trim();

    if (!question) {
        showError('Please enter a question');
        return;
    }

    // Disable input and show loading
    setLoading(true);

    // Add user message to chat
    addMessage(question, 'user');

    // Clear input
    questionInput.value = '';

    try {
        // Send request to backend
        const response = await fetch('/ask', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ question: question })
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || 'Server error');
        }

        // Add AI response to chat
        addMessage(data.answer, 'ai');

    } catch (error) {
        console.error('Error:', error);
        showError('Failed to get AI response: ' + error.message);
    } finally {
        setLoading(false);
    }
}

// Add message to chat history
function addMessage(text, sender) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;
    messageDiv.textContent = text;

    chatHistory.appendChild(messageDiv);
    chatHistory.scrollTop = chatHistory.scrollHeight;
}

// Show error message
function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error';
    errorDiv.textContent = message;

    chatHistory.appendChild(errorDiv);
    chatHistory.scrollTop = chatHistory.scrollHeight;

    // Remove error after 5 seconds
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.parentNode.removeChild(errorDiv);
        }
    }, 5000);
}

// Toggle loading state
function setLoading(isLoading) {
    askButton.disabled = isLoading;
    questionInput.disabled = isLoading;

    if (isLoading) {
        loading.classList.remove('hidden');
        askButton.textContent = 'Thinking...';
    } else {
        loading.classList.add('hidden');
        askButton.textContent = 'Ask AI';
        questionInput.focus();
    }
}

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    questionInput.focus();
});
```

### data/history.json (template)
```json
[]
```

### .env (template)
```env
# OpenRouter API Key
# Get your API key from: https://openrouter.ai/keys
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Flask configuration
FLASK_ENV=development
FLASK_DEBUG=True
```

## Setup Instructions

### 1. Create Project Directory
```bash
mkdir ai_assistant
cd ai_assistant
```

### 2. Install Dependencies
```bash
pip install flask pydantic python-dotenv requests
```

### 3. Create Directory Structure
```bash
mkdir templates static data
```

### 4. Create Files
Create all the files listed above with their respective content.

### 5. Configure Environment
1. Copy the `.env` template and add your OpenRouter API key
2. Get your API key from: https://openrouter.ai/keys
3. Replace `your_openrouter_api_key_here` with your actual API key

### 6. Run the Application
```bash
flask run
```

Or alternatively:
```bash
python app.py
```

The application will be available at: http://localhost:5000

## Features

- **Simple Interface**: Clean, responsive web interface with a textarea for questions and chat-style display for responses
- **Real-time Communication**: Uses vanilla JavaScript fetch API to communicate with the Flask backend
- **Conversation History**: Automatically saves all Q&A pairs to `data/history.json` with timestamps
- **Error Handling**: Comprehensive error handling for API failures, validation errors, and network issues
- **Loading States**: Visual feedback during AI processing with spinner and disabled controls
- **Keyboard Shortcuts**: Press Enter to submit questions (Shift+Enter for new lines)
- **Responsive Design**: Works well on desktop and mobile devices

## API Endpoints

### GET /
- **Description**: Serves the main HTML page
- **Response**: HTML page with the chat interface

### POST /ask
- **Description**: Accepts user questions and returns AI responses
- **Request Body**: `{ "question": "your question here" }`
- **Response**: `{ "answer": "AI response here" }`
- **Error Responses**:
  - 400: Invalid request format
  - 500: Internal server error

## Technical Details

- **Backend**: Flask with Pydantic for JSON validation
- **Frontend**: Vanilla HTML/CSS/JavaScript (no frameworks)
- **AI Service**: OpenRouter API (supports multiple LLM providers)
- **Data Storage**: Local JSON file for conversation history
- **Default Model**: Anthropic Claude 3 Haiku (configurable in agents.py)
- **Security**: Environment variables for API keys
- **Error Handling**: Graceful degradation with user-friendly error messages

## Customization Options

- **Change AI Model**: Modify the `model` parameter in `agents.py`
- **Adjust Response Length**: Change `max_tokens` in the API payload
- **Modify System Prompt**: Update the system message in `agents.py`
- **Styling**: Customize appearance by editing `static/style.css`
- **Add Features**: Extend functionality by modifying the Flask routes and JavaScript

This minimal setup provides a fully functional AI coding assistant that runs entirely locally with no external dependencies beyond the OpenRouter API for AI responses.
