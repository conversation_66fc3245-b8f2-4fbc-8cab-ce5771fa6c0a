#!/usr/bin/env python3
"""
AI Coding Assistant - Demo Version
A simplified version for testing the UI without requiring API keys
"""

import json
import os
import uuid
from datetime import datetime
from pathlib import Path

from flask import Flask, jsonify, render_template, request, session
from pydantic import BaseModel, ValidationError

# Demo responses for testing
DEMO_RESPONSES = [
    {
        "explanation": "To implement authentication in Flask, you'll want to use Flask-Login for session management. Here's a comprehensive approach:",
        "code_snippet": """from flask import Flask, render_template, request, redirect, url_for, flash
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash

app = Flask(__name__)
app.secret_key = 'your-secret-key'

# Initialize Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

class User(UserMixin):
    def __init__(self, id, username, password_hash):
        self.id = id
        self.username = username
        self.password_hash = password_hash

@login_manager.user_loader
def load_user(user_id):
    # In a real app, load from database
    return User.get(user_id)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        # Verify credentials (use database in real app)
        user = authenticate_user(username, password)
        if user:
            login_user(user)
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid credentials')
    
    return render_template('login.html')

@app.route('/dashboard')
@login_required
def dashboard():
    return f'Welcome {current_user.username}!'""",
        "best_practices": [
            "Always hash passwords using werkzeug.security or bcrypt",
            "Use HTTPS in production to protect login credentials",
            "Implement proper session management with secure cookies",
            "Add CSRF protection for forms",
            "Consider implementing rate limiting for login attempts",
            "Use strong, random secret keys",
            "Implement proper logout functionality"
        ],
        "additional_resources": [
            "https://flask-login.readthedocs.io/",
            "https://flask.palletsprojects.com/en/2.3.x/tutorial/auth/",
            "https://owasp.org/www-project-web-security-testing-guide/"
        ],
        "confidence_score": 0.95,
        "follow_up_suggestions": [
            "How do I add password reset functionality?",
            "What's the best way to handle user roles and permissions?",
            "How can I implement OAuth login with Google or GitHub?"
        ],
        "response_type": "code_generation",
        "language": "python"
    },
    {
        "explanation": "Here's how to create a REST API in Python using Flask with proper error handling and validation:",
        "code_snippet": """from flask import Flask, request, jsonify
from pydantic import BaseModel, ValidationError
from typing import List, Optional

app = Flask(__name__)

# Pydantic models for validation
class User(BaseModel):
    id: Optional[int] = None
    name: str
    email: str
    age: int

class UserResponse(BaseModel):
    id: int
    name: str
    email: str
    age: int

# In-memory storage (use database in production)
users = []
next_id = 1

@app.route('/api/users', methods=['GET'])
def get_users():
    return jsonify([user.dict() for user in users])

@app.route('/api/users', methods=['POST'])
def create_user():
    try:
        user_data = User(**request.json)
        user_data.id = next_id
        users.append(user_data)
        next_id += 1
        
        return jsonify(user_data.dict()), 201
    except ValidationError as e:
        return jsonify({'error': 'Validation failed', 'details': e.errors()}), 400

@app.route('/api/users/<int:user_id>', methods=['GET'])
def get_user(user_id):
    user = next((u for u in users if u.id == user_id), None)
    if not user:
        return jsonify({'error': 'User not found'}), 404
    return jsonify(user.dict())

@app.route('/api/users/<int:user_id>', methods=['PUT'])
def update_user(user_id):
    user_index = next((i for i, u in enumerate(users) if u.id == user_id), None)
    if user_index is None:
        return jsonify({'error': 'User not found'}), 404
    
    try:
        updated_data = User(**request.json)
        updated_data.id = user_id
        users[user_index] = updated_data
        return jsonify(updated_data.dict())
    except ValidationError as e:
        return jsonify({'error': 'Validation failed', 'details': e.errors()}), 400

if __name__ == '__main__':
    app.run(debug=True)""",
        "best_practices": [
            "Use Pydantic or similar for request/response validation",
            "Implement proper HTTP status codes",
            "Add comprehensive error handling",
            "Use blueprints to organize larger APIs",
            "Implement pagination for list endpoints",
            "Add API versioning (e.g., /api/v1/)",
            "Use proper HTTP methods (GET, POST, PUT, DELETE)",
            "Add authentication and authorization"
        ],
        "additional_resources": [
            "https://flask-restful.readthedocs.io/",
            "https://pydantic-docs.helpmanual.io/",
            "https://restfulapi.net/"
        ],
        "confidence_score": 0.92,
        "follow_up_suggestions": [
            "How do I add database integration with SQLAlchemy?",
            "What's the best way to handle API authentication?",
            "How can I add API documentation with Swagger?"
        ],
        "response_type": "code_generation",
        "language": "python"
    }
]

class QuestionRequest(BaseModel):
    question: str
    context: dict = {}
    session_id: str = None

class AnswerResponse(BaseModel):
    answer: dict
    session_id: str
    timestamp: str
    processing_time_ms: int
    confidence_score: float = None

def create_app():
    app = Flask(__name__)
    app.secret_key = 'demo-secret-key'
    
    # Ensure data directory exists
    Path('data').mkdir(exist_ok=True)
    
    @app.route('/')
    def index():
        if 'session_id' not in session:
            session['session_id'] = str(uuid.uuid4())
        return render_template('index.html', session_id=session['session_id'])
    
    @app.route('/ask', methods=['POST'])
    def ask_ai():
        start_time = datetime.now()
        
        try:
            # Validate request
            data = request.get_json()
            question_req = QuestionRequest(**data)
            
            # Get or create session ID
            session_id = question_req.session_id or session.get('session_id', str(uuid.uuid4()))
            if 'session_id' not in session:
                session['session_id'] = session_id
            
            # Simulate processing time
            import time
            time.sleep(1)  # Simulate AI processing
            
            # Select a demo response based on question content
            question_lower = question_req.question.lower()
            if 'auth' in question_lower or 'login' in question_lower:
                demo_response = DEMO_RESPONSES[0]
            elif 'api' in question_lower or 'rest' in question_lower:
                demo_response = DEMO_RESPONSES[1]
            else:
                # Default response
                demo_response = {
                    "explanation": f"Great question about: '{question_req.question}'\n\nThis is a demo response. In the full version, this would be powered by advanced AI models that provide comprehensive, accurate answers to your programming questions.",
                    "code_snippet": "# Demo code snippet\nprint('This is a demo response!')\n# The real AI would provide relevant code here",
                    "best_practices": [
                        "This is a demo - real responses include actual best practices",
                        "The full version uses multiple AI models for accuracy",
                        "Responses are tailored to your specific question"
                    ],
                    "additional_resources": [
                        "https://example.com/demo-resource"
                    ],
                    "confidence_score": 0.85,
                    "follow_up_suggestions": [
                        "This is a demo - what would you like to know about the real AI assistant?",
                        "How can I help you with your programming questions?"
                    ],
                    "response_type": "general_programming",
                    "language": "python"
                }
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            
            # Create response
            response = AnswerResponse(
                answer=demo_response,
                session_id=session_id,
                timestamp=start_time.isoformat(),
                processing_time_ms=int(processing_time),
                confidence_score=demo_response.get('confidence_score', 0.85)
            )
            
            # Save to history
            save_to_history(
                session_id=session_id,
                question=question_req.question,
                answer=demo_response['explanation'],
                response_data=demo_response,
                processing_time_ms=int(processing_time)
            )
            
            return jsonify(response.model_dump())
            
        except ValidationError as e:
            return jsonify({
                "error": "validation_error",
                "message": "Invalid request data",
                "details": e.errors(),
                "timestamp": datetime.now().isoformat()
            }), 400
        except Exception as e:
            return jsonify({
                "error": "internal_error",
                "message": "An error occurred processing your request",
                "timestamp": datetime.now().isoformat()
            }), 500
    
    @app.route('/history')
    def get_history():
        session_id = session.get('session_id')
        if not session_id:
            return jsonify({"history": []})
        
        try:
            history = load_history()
            session_history = [
                entry for entry in history
                if entry.get('session_id') == session_id
            ]
            session_history.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
            return jsonify({"history": session_history[:50]})
        except Exception:
            return jsonify({"history": []})
    
    @app.route('/health')
    def health_check():
        return jsonify({
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0-demo",
            "mode": "demo"
        })
    
    def save_to_history(session_id, question, answer, response_data=None, processing_time_ms=0):
        try:
            history = load_history()
            entry = {
                "id": str(uuid.uuid4()),
                "session_id": session_id,
                "timestamp": datetime.now().isoformat(),
                "question": question,
                "answer": answer,
                "error": False,
                "processing_time_ms": processing_time_ms,
                "response_data": response_data
            }
            history.append(entry)
            
            # Keep only recent entries
            if len(history) > 100:
                history = history[-100:]
            
            with open('data/history.json', 'w', encoding='utf-8') as f:
                json.dump(history, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Failed to save history: {e}")
    
    def load_history():
        try:
            if Path('data/history.json').exists():
                with open('data/history.json', 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception:
            return []
    
    return app

if __name__ == '__main__':
    print("🚀 Starting AI Coding Assistant (Demo Mode)")
    print("🌐 Demo server starting on http://localhost:5000")
    print("📝 This is a demo version - no API key required!")
    print("⏹️  Press Ctrl+C to stop the server")
    
    app = create_app()
    app.run(host='0.0.0.0', port=5000, debug=True)
