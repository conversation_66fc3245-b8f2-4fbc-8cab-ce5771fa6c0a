"""
Advanced AI Coding Assistant Flask Application
Production-ready implementation with comprehensive error handling,
security features, and modern Flask patterns.
"""

import json
import logging
import os
import uuid
from datetime import datetime, timedelta
from functools import wraps
from pathlib import Path
from typing import Any, Dict, Optional, Union
import asyncio
import threading

try:
    from flask import Flask, jsonify, render_template, request, session
    from pydantic import BaseModel, ValidationError
    from werkzeug.exceptions import HTTPException
    import httpx
    from agents import AIAgentSystem, AIAgentDependencies, CodingResponse, ErrorResponse
except ImportError as e:
    print(f"Missing required dependency: {e}")
    print("Please install requirements: pip install -r requirements.txt")
    exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class Config:
    """Application configuration with environment-based settings"""
    
    # Core Flask settings
    SECRET_KEY = os.getenv('SECRET_KEY', os.urandom(32).hex())
    DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    TESTING = os.getenv('FLASK_TESTING', 'False').lower() == 'true'
    
    # OpenRouter API settings
    OPENROUTER_API_KEY = os.getenv('OPENROUTER_API_KEY')
    OPENROUTER_BASE_URL = os.getenv('OPENROUTER_BASE_URL', 'https://openrouter.ai/api/v1')
    
    # Rate limiting and security
    MAX_REQUESTS_PER_MINUTE = int(os.getenv('MAX_REQUESTS_PER_MINUTE', '30'))
    MAX_TOKENS_PER_REQUEST = int(os.getenv('MAX_TOKENS_PER_REQUEST', '4000'))
    SESSION_TIMEOUT_MINUTES = int(os.getenv('SESSION_TIMEOUT_MINUTES', '60'))
    
    # File paths
    DATA_DIR = Path('data')
    HISTORY_FILE = DATA_DIR / 'history.json'
    LOGS_DIR = Path('logs')
    
    # JSON settings
    JSON_SORT_KEYS = False
    JSONIFY_PRETTYPRINT_REGULAR = not DEBUG  # Minimize JSON in production


# Pydantic models for request/response validation
class QuestionRequest(BaseModel):
    """Validated request model for AI questions"""
    question: str
    context: Optional[Dict[str, Any]] = None
    session_id: Optional[str] = None
    
    class Config:
        str_strip_whitespace = True
        min_anystr_length = 1
        max_anystr_length = 5000


class AnswerResponse(BaseModel):
    """Validated response model for AI answers"""
    answer: Union[CodingResponse, str]
    session_id: str
    timestamp: str
    processing_time_ms: int
    model_used: Optional[str] = None
    confidence_score: Optional[float] = None


class ErrorResponseModel(BaseModel):
    """Standardized error response model"""
    error: str
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: str
    request_id: str
    suggestion: Optional[str] = None
    retry_after: Optional[int] = None


def create_app(config_class=Config) -> Flask:
    """Application factory with comprehensive configuration"""
    
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # Ensure required directories exist
    config_class.DATA_DIR.mkdir(exist_ok=True)
    config_class.LOGS_DIR.mkdir(exist_ok=True)
    
    # Validate required configuration
    if not app.config['OPENROUTER_API_KEY']:
        raise ValueError("OPENROUTER_API_KEY environment variable is required")
    
    # Configure session
    app.permanent_session_lifetime = timedelta(minutes=app.config['SESSION_TIMEOUT_MINUTES'])
    
    # Global AI agent system (initialized on first request)
    app.ai_agent = None
    app.http_client = None
    
    # Request tracking for rate limiting
    app.request_tracker = {}
    
    def initialize_ai_system():
        """Initialize AI system lazily"""
        if not hasattr(app, 'ai_agent') or app.ai_agent is None:
            try:
                import asyncio

                # Create HTTP client
                app.http_client = httpx.AsyncClient(
                    timeout=httpx.Timeout(30.0),
                    limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)
                )

                # Create dependencies
                deps = AIAgentDependencies(
                    api_key=app.config['OPENROUTER_API_KEY'],
                    http_client=app.http_client,
                    rate_limit_per_minute=app.config['MAX_REQUESTS_PER_MINUTE'],
                    max_tokens=app.config['MAX_TOKENS_PER_REQUEST']
                )

                # Create AI agent system
                app.ai_agent = AIAgentSystem(deps)
                logger.info("AI system initialized successfully")

            except Exception as e:
                logger.error(f"Failed to initialize AI system: {e}")
                app.ai_agent = None
    
    @app.teardown_appcontext
    def cleanup_resources(error):
        """Clean up resources on app context teardown"""
        if error:
            logger.error(f"App context error: {error}")
    
    # Rate limiting decorator
    def rate_limit(max_requests: int = None):
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
                current_time = datetime.now()
                
                # Clean old entries
                cutoff_time = current_time - timedelta(minutes=1)
                if client_ip in app.request_tracker:
                    app.request_tracker[client_ip] = [
                        req_time for req_time in app.request_tracker[client_ip]
                        if req_time > cutoff_time
                    ]
                
                # Check rate limit
                max_reqs = max_requests or app.config['MAX_REQUESTS_PER_MINUTE']
                if client_ip in app.request_tracker:
                    if len(app.request_tracker[client_ip]) >= max_reqs:
                        return jsonify(ErrorResponseModel(
                            error="rate_limit_exceeded",
                            message=f"Rate limit exceeded. Maximum {max_reqs} requests per minute.",
                            timestamp=current_time.isoformat(),
                            request_id=str(uuid.uuid4()),
                            retry_after=60
                        ).model_dump()), 429
                
                # Track this request
                if client_ip not in app.request_tracker:
                    app.request_tracker[client_ip] = []
                app.request_tracker[client_ip].append(current_time)
                
                return f(*args, **kwargs)
            return decorated_function
        return decorator
    
    # Global error handlers
    @app.errorhandler(HTTPException)
    def handle_http_exception(e):
        """Handle HTTP exceptions with JSON responses"""
        request_id = str(uuid.uuid4())
        
        error_response = ErrorResponseModel(
            error=e.name.lower().replace(' ', '_'),
            message=e.description,
            timestamp=datetime.now().isoformat(),
            request_id=request_id
        )
        
        logger.warning(f"HTTP {e.code} error: {e.description} (Request ID: {request_id})")
        return jsonify(error_response.model_dump()), e.code
    
    @app.errorhandler(ValidationError)
    def handle_validation_error(e):
        """Handle Pydantic validation errors"""
        request_id = str(uuid.uuid4())
        
        error_response = ErrorResponseModel(
            error="validation_error",
            message="Invalid request data",
            details={"validation_errors": e.errors()},
            timestamp=datetime.now().isoformat(),
            request_id=request_id,
            suggestion="Please check your request format and try again"
        )
        
        logger.warning(f"Validation error: {e} (Request ID: {request_id})")
        return jsonify(error_response.model_dump()), 400
    
    @app.errorhandler(Exception)
    def handle_generic_exception(e):
        """Handle unexpected exceptions"""
        request_id = str(uuid.uuid4())
        
        if isinstance(e, HTTPException):
            return handle_http_exception(e)
        
        logger.error(f"Unexpected error: {e} (Request ID: {request_id})", exc_info=True)
        
        error_response = ErrorResponseModel(
            error="internal_server_error",
            message="An unexpected error occurred",
            timestamp=datetime.now().isoformat(),
            request_id=request_id,
            suggestion="Please try again later or contact support if the issue persists"
        )
        
        return jsonify(error_response.model_dump()), 500
    
    # Routes
    @app.route('/')
    def index():
        """Serve the main application page"""
        # Generate or retrieve session ID
        if 'session_id' not in session:
            session['session_id'] = str(uuid.uuid4())
            session.permanent = True
        
        return render_template('index.html', session_id=session['session_id'])
    
    @app.route('/ask', methods=['POST'])
    @rate_limit()
    def ask_ai():
        """Main AI question endpoint with comprehensive processing"""
        start_time = datetime.now()
        request_id = str(uuid.uuid4())

        try:
            # Validate request data
            data = request.get_json()
            if not data:
                raise ValidationError("No JSON data provided", model=QuestionRequest)

            question_req = QuestionRequest(**data)

            # Get or create session ID
            session_id = question_req.session_id or session.get('session_id', str(uuid.uuid4()))
            if 'session_id' not in session:
                session['session_id'] = session_id
                session.permanent = True

            # Initialize AI system if needed
            if not app.ai_agent:
                initialize_ai_system()

            if not app.ai_agent:
                raise Exception("AI system not available")

            # Update dependencies with session info
            app.ai_agent.deps.session_id = session_id
            app.ai_agent.deps.user_id = request.environ.get('HTTP_X_USER_ID')

            logger.info(f"Processing question from session {session_id}: {question_req.question[:100]}...")

            # Get AI response (using sync wrapper)
            import asyncio
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                ai_response = loop.run_until_complete(
                    app.ai_agent.get_response(question_req.question, question_req.context)
                )
            finally:
                loop.close()

            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds() * 1000

            # Prepare response data
            if isinstance(ai_response, ErrorResponse):
                # Handle AI-generated errors
                error_response = ErrorResponseModel(
                    error=ai_response.error_type,
                    message=ai_response.message,
                    timestamp=start_time.isoformat(),
                    request_id=request_id,
                    suggestion=ai_response.suggestion,
                    retry_after=ai_response.retry_after
                )

                # Save error to history
                save_to_history(
                    session_id=session_id,
                    question=question_req.question,
                    answer=ai_response.message,
                    error=True,
                    processing_time_ms=int(processing_time)
                )

                return jsonify(error_response.model_dump()), 400

            # Successful response
            response = AnswerResponse(
                answer=ai_response,
                session_id=session_id,
                timestamp=start_time.isoformat(),
                processing_time_ms=int(processing_time),
                confidence_score=getattr(ai_response, 'confidence_score', None)
            )

            # Save successful interaction to history
            save_to_history(
                session_id=session_id,
                question=question_req.question,
                answer=ai_response.explanation if hasattr(ai_response, 'explanation') else str(ai_response),
                response_data=ai_response.model_dump() if hasattr(ai_response, 'model_dump') else None,
                processing_time_ms=int(processing_time)
            )

            logger.info(f"Successfully processed question for session {session_id} in {processing_time:.2f}ms")
            return jsonify(response.model_dump())

        except ValidationError as e:
            raise  # Let the error handler deal with it
        except Exception as e:
            logger.error(f"Error processing question: {e}", exc_info=True)

            error_response = ErrorResponseModel(
                error="processing_error",
                message="Failed to process your question",
                timestamp=start_time.isoformat(),
                request_id=request_id,
                suggestion="Please try rephrasing your question or try again later"
            )

            return jsonify(error_response.model_dump()), 500

    @app.route('/history')
    def get_history():
        """Get conversation history for the current session"""
        session_id = session.get('session_id')
        if not session_id:
            return jsonify({"history": []})

        try:
            history = load_history()
            session_history = [
                entry for entry in history
                if entry.get('session_id') == session_id
            ]

            # Sort by timestamp and limit to recent entries
            session_history.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
            return jsonify({"history": session_history[:50]})  # Last 50 entries

        except Exception as e:
            logger.error(f"Error loading history: {e}")
            return jsonify({"history": [], "error": "Failed to load history"})

    @app.route('/health')
    def health_check():
        """Health check endpoint for monitoring"""
        return jsonify({
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "ai_system_ready": app.ai_agent is not None
        })

    # Utility functions
    def save_to_history(
        session_id: str,
        question: str,
        answer: str,
        error: bool = False,
        response_data: Optional[Dict] = None,
        processing_time_ms: int = 0
    ):
        """Save conversation entry to history with error handling"""
        try:
            history = load_history()

            entry = {
                "id": str(uuid.uuid4()),
                "session_id": session_id,
                "timestamp": datetime.now().isoformat(),
                "question": question,
                "answer": answer,
                "error": error,
                "processing_time_ms": processing_time_ms,
                "response_data": response_data
            }

            history.append(entry)

            # Keep only recent entries to prevent file from growing too large
            if len(history) > 1000:
                history = history[-1000:]

            # Save to file
            with open(app.config['HISTORY_FILE'], 'w', encoding='utf-8') as f:
                json.dump(history, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"Failed to save to history: {e}")

    def load_history() -> list:
        """Load conversation history from file"""
        try:
            if app.config['HISTORY_FILE'].exists():
                with open(app.config['HISTORY_FILE'], 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            logger.error(f"Failed to load history: {e}")
            return []

    return app


# Create the Flask application
app = create_app()


if __name__ == '__main__':
    # Development server
    app.run(
        host='0.0.0.0',
        port=int(os.getenv('PORT', 5000)),
        debug=app.config['DEBUG']
    )
