/**
 * Service Worker for AI Coding Assistant
 * Provides offline support and caching for better performance
 */

const CACHE_NAME = 'ai-assistant-v1.0.0';
const STATIC_CACHE_URLS = [
    '/',
    '/static/style.css',
    '/static/app.js',
    '/health'
];

// Install event - cache static resources
self.addEventListener('install', (event) => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then((cache) => {
                console.log('Service Worker: Caching static resources');
                return cache.addAll(STATIC_CACHE_URLS);
            })
            .then(() => {
                console.log('Service Worker: Installation complete');
                return self.skipWaiting();
            })
            .catch((error) => {
                console.error('Service Worker: Installation failed', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then((cacheNames) => {
                return Promise.all(
                    cacheNames.map((cacheName) => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activation complete');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve from cache when offline
self.addEventListener('fetch', (event) => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Handle API requests
    if (url.pathname.startsWith('/ask')) {
        event.respondWith(
            fetch(request)
                .then((response) => {
                    // If online, return the response and cache it
                    if (response.ok) {
                        const responseClone = response.clone();
                        caches.open(CACHE_NAME)
                            .then((cache) => {
                                cache.put(request, responseClone);
                            });
                    }
                    return response;
                })
                .catch(() => {
                    // If offline, try to serve from cache or return offline message
                    return caches.match(request)
                        .then((cachedResponse) => {
                            if (cachedResponse) {
                                return cachedResponse;
                            }
                            
                            // Return offline response
                            return new Response(
                                JSON.stringify({
                                    answer: {
                                        explanation: "You're currently offline. Please check your internet connection and try again.",
                                        response_type: "error",
                                        confidence_score: 0
                                    },
                                    session_id: "offline",
                                    timestamp: new Date().toISOString(),
                                    processing_time_ms: 0,
                                    offline: true
                                }),
                                {
                                    status: 200,
                                    statusText: 'OK',
                                    headers: {
                                        'Content-Type': 'application/json'
                                    }
                                }
                            );
                        });
                })
        );
        return;
    }
    
    // Handle static resources
    if (STATIC_CACHE_URLS.includes(url.pathname) || url.pathname.startsWith('/static/')) {
        event.respondWith(
            caches.match(request)
                .then((cachedResponse) => {
                    if (cachedResponse) {
                        return cachedResponse;
                    }
                    
                    return fetch(request)
                        .then((response) => {
                            if (response.ok) {
                                const responseClone = response.clone();
                                caches.open(CACHE_NAME)
                                    .then((cache) => {
                                        cache.put(request, responseClone);
                                    });
                            }
                            return response;
                        });
                })
        );
        return;
    }
    
    // For all other requests, try network first, then cache
    event.respondWith(
        fetch(request)
            .catch(() => {
                return caches.match(request);
            })
    );
});

// Background sync for queued requests when back online
self.addEventListener('sync', (event) => {
    if (event.tag === 'background-sync') {
        console.log('Service Worker: Background sync triggered');
        event.waitUntil(
            // Process any queued requests here
            processQueuedRequests()
        );
    }
});

// Push notifications (for future features)
self.addEventListener('push', (event) => {
    if (event.data) {
        const data = event.data.json();
        const options = {
            body: data.body,
            icon: '/static/icon-192.png',
            badge: '/static/badge-72.png',
            vibrate: [100, 50, 100],
            data: {
                dateOfArrival: Date.now(),
                primaryKey: data.primaryKey
            },
            actions: [
                {
                    action: 'explore',
                    title: 'View',
                    icon: '/static/checkmark.png'
                },
                {
                    action: 'close',
                    title: 'Close',
                    icon: '/static/xmark.png'
                }
            ]
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );
    }
});

// Notification click handler
self.addEventListener('notificationclick', (event) => {
    event.notification.close();
    
    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

// Helper function to process queued requests
async function processQueuedRequests() {
    try {
        // Implementation for processing offline queued requests
        console.log('Processing queued requests...');
        
        // Get queued requests from IndexedDB or localStorage
        // Process them when back online
        
        return Promise.resolve();
    } catch (error) {
        console.error('Error processing queued requests:', error);
        return Promise.reject(error);
    }
}

// Message handler for communication with main thread
self.addEventListener('message', (event) => {
    const { type, payload } = event.data;
    
    switch (type) {
        case 'SKIP_WAITING':
            self.skipWaiting();
            break;
            
        case 'GET_VERSION':
            event.ports[0].postMessage({
                type: 'VERSION',
                payload: { version: CACHE_NAME }
            });
            break;
            
        case 'CLEAR_CACHE':
            caches.delete(CACHE_NAME)
                .then(() => {
                    event.ports[0].postMessage({
                        type: 'CACHE_CLEARED',
                        payload: { success: true }
                    });
                })
                .catch((error) => {
                    event.ports[0].postMessage({
                        type: 'CACHE_CLEARED',
                        payload: { success: false, error: error.message }
                    });
                });
            break;
            
        default:
            console.log('Service Worker: Unknown message type', type);
    }
});

console.log('Service Worker: Script loaded successfully');
