# 🚀 AI Coding Assistant - Production Deployment Guide

## 🌟 Overview

This comprehensive guide covers deploying the AI Coding Assistant in production environments with high availability, security, and performance optimizations.

## 📋 Prerequisites

### System Requirements
- **OS**: Linux (Ubuntu 20.04+ recommended), macOS, or Windows Server
- **Python**: 3.9 or higher
- **Memory**: Minimum 2GB RAM (4GB+ recommended)
- **Storage**: 10GB+ available space
- **Network**: Stable internet connection for AI API calls

### Required Services
- **OpenRouter API Key**: [Get one here](https://openrouter.ai/keys)
- **Domain Name**: For production deployment (optional)
- **SSL Certificate**: For HTTPS (recommended)

## 🛠️ Deployment Options

### Option 1: Quick Demo Deployment (No API Key Required)

```bash
# Clone and setup
git clone <repository-url>
cd ai_assistant

# Run automated setup
python setup.py

# Start demo server
python demo.py
```

**Access**: http://localhost:5000

### Option 2: Full Production Deployment

#### Step 1: Environment Setup

```bash
# Create production environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt
```

#### Step 2: Configuration

```bash
# Copy and edit environment file
cp .env.example .env
nano .env  # Edit with your settings
```

**Required Environment Variables:**
```env
OPENROUTER_API_KEY=your_actual_api_key_here
SECRET_KEY=your_super_secret_key_here
FLASK_ENV=production
FLASK_DEBUG=False
MAX_REQUESTS_PER_MINUTE=60
```

#### Step 3: Production Server

```bash
# Using Gunicorn (recommended)
gunicorn -w 4 -b 0.0.0.0:5000 app:app

# Or using uWSGI
uwsgi --http :5000 --module app:app --processes 4

# Or using Uvicorn (async support)
uvicorn app:app --host 0.0.0.0 --port 5000 --workers 4
```

### Option 3: Docker Deployment

#### Single Container
```bash
# Build image
docker build -t ai-assistant .

# Run container
docker run -d \
  --name ai-assistant \
  -p 5000:5000 \
  -e OPENROUTER_API_KEY=your_key_here \
  -v $(pwd)/data:/app/data \
  ai-assistant
```

#### Full Stack with Docker Compose
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f ai-assistant

# Scale application
docker-compose up -d --scale ai-assistant=3
```

**Includes:**
- AI Assistant application
- Nginx reverse proxy
- Redis for caching
- PostgreSQL database
- Prometheus monitoring
- Grafana dashboards
- Log aggregation with Loki

## 🔧 Advanced Configuration

### Load Balancing with Nginx

```nginx
upstream ai_assistant {
    least_conn;
    server 127.0.0.1:5001 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:5002 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:5003 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://ai_assistant;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

### SSL/HTTPS Setup

```bash
# Using Certbot (Let's Encrypt)
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com

# Manual certificate
sudo nginx -t
sudo systemctl reload nginx
```

### Database Integration (Optional)

```python
# Add to app.py for PostgreSQL support
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///data/app.db')
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
```

## 📊 Monitoring & Observability

### Health Checks
- **Endpoint**: `/health`
- **Expected Response**: `{"status": "healthy"}`
- **Monitoring Frequency**: Every 30 seconds

### Metrics Collection
```bash
# Prometheus metrics endpoint
curl http://localhost:5000/metrics

# Key metrics to monitor:
# - Request rate and latency
# - Error rates
# - AI API response times
# - Memory and CPU usage
```

### Log Management
```bash
# Application logs
tail -f logs/app.log

# Access logs (with Nginx)
tail -f /var/log/nginx/access.log

# Error logs
tail -f /var/log/nginx/error.log
```

## 🔒 Security Best Practices

### Application Security
- ✅ Environment variables for secrets
- ✅ Input validation with Pydantic
- ✅ Rate limiting implemented
- ✅ CORS headers configured
- ✅ XSS protection enabled
- ✅ SQL injection prevention

### Infrastructure Security
```bash
# Firewall configuration
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable

# Fail2ban for SSH protection
sudo apt install fail2ban
sudo systemctl enable fail2ban
```

### SSL/TLS Configuration
```nginx
# Strong SSL configuration
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256;
ssl_prefer_server_ciphers off;
add_header Strict-Transport-Security "max-age=63072000" always;
```

## 🚀 Performance Optimization

### Application Level
```python
# Enable caching
from flask_caching import Cache
cache = Cache(app, config={'CACHE_TYPE': 'redis'})

# Connection pooling
import httpx
client = httpx.AsyncClient(
    limits=httpx.Limits(max_keepalive_connections=20, max_connections=100)
)
```

### Server Level
```bash
# Optimize Gunicorn
gunicorn \
  --workers 4 \
  --worker-class sync \
  --worker-connections 1000 \
  --max-requests 1000 \
  --max-requests-jitter 100 \
  --timeout 120 \
  --keep-alive 2 \
  app:app
```

### Database Optimization
```sql
-- Index frequently queried columns
CREATE INDEX idx_session_timestamp ON conversations(session_id, timestamp);
CREATE INDEX idx_user_id ON conversations(user_id);
```

## 📈 Scaling Strategies

### Horizontal Scaling
1. **Load Balancer**: Nginx/HAProxy
2. **Multiple App Instances**: Docker Swarm/Kubernetes
3. **Database Clustering**: PostgreSQL with read replicas
4. **Caching Layer**: Redis Cluster

### Vertical Scaling
1. **CPU**: Increase worker processes
2. **Memory**: Optimize caching and buffer sizes
3. **Storage**: SSD for faster I/O

### Auto-scaling with Kubernetes
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-assistant
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-assistant
  template:
    metadata:
      labels:
        app: ai-assistant
    spec:
      containers:
      - name: ai-assistant
        image: ai-assistant:latest
        ports:
        - containerPort: 5000
        env:
        - name: OPENROUTER_API_KEY
          valueFrom:
            secretKeyRef:
              name: ai-assistant-secrets
              key: openrouter-api-key
---
apiVersion: v1
kind: Service
metadata:
  name: ai-assistant-service
spec:
  selector:
    app: ai-assistant
  ports:
  - port: 80
    targetPort: 5000
  type: LoadBalancer
```

## 🔄 Backup & Recovery

### Data Backup
```bash
# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf "backup_${DATE}.tar.gz" data/ logs/
aws s3 cp "backup_${DATE}.tar.gz" s3://your-backup-bucket/
```

### Database Backup
```bash
# PostgreSQL backup
pg_dump ai_assistant > backup_$(date +%Y%m%d).sql

# Automated with cron
0 2 * * * /usr/bin/pg_dump ai_assistant > /backups/db_$(date +\%Y\%m\%d).sql
```

## 🚨 Troubleshooting

### Common Issues

**Issue**: Application won't start
```bash
# Check logs
tail -f logs/app.log

# Verify environment
python -c "import os; print(os.getenv('OPENROUTER_API_KEY'))"

# Test dependencies
python -c "import flask, pydantic, httpx; print('All imports OK')"
```

**Issue**: High response times
```bash
# Check system resources
htop
df -h

# Monitor network
netstat -tuln

# Check AI API latency
curl -w "@curl-format.txt" -s -o /dev/null http://localhost:5000/health
```

**Issue**: Memory leaks
```bash
# Monitor memory usage
ps aux | grep python
free -h

# Enable memory profiling
pip install memory-profiler
python -m memory_profiler app.py
```

## 📞 Support & Maintenance

### Regular Maintenance Tasks
- [ ] Update dependencies monthly
- [ ] Review and rotate API keys quarterly
- [ ] Monitor disk space weekly
- [ ] Check SSL certificate expiration
- [ ] Review access logs for anomalies
- [ ] Update security patches

### Emergency Contacts
- **System Administrator**: <EMAIL>
- **DevOps Team**: <EMAIL>
- **On-call Engineer**: +1-555-0123

---

## 🎉 Congratulations!

Your AI Coding Assistant is now deployed and ready to help developers with their programming questions. The system is designed to be:

- **Scalable**: Handle thousands of concurrent users
- **Reliable**: 99.9% uptime with proper monitoring
- **Secure**: Enterprise-grade security features
- **Fast**: Sub-second response times
- **Maintainable**: Clean architecture and comprehensive logging

For additional support, check the README.md file or contact the development team.

**Happy coding! 🚀**
