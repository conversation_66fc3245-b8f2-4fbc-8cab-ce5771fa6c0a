/* 
Advanced AI Coding Assistant Styles
Modern, responsive design with accessibility and performance optimizations
*/

/* CSS Custom Properties (Variables) */
:root {
  /* Color Palette */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-900: #1e3a8a;
  
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  --success-500: #10b981;
  --warning-500: #f59e0b;
  --error-500: #ef4444;
  
  /* Typography */
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', '<PERSON><PERSON>', <PERSON><PERSON><PERSON>, 'Liberation Mono', Menlo, monospace;
  
  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Z-index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Dark Theme Variables */
[data-theme="dark"] {
  --bg-primary: var(--gray-900);
  --bg-secondary: var(--gray-800);
  --bg-tertiary: var(--gray-700);
  --text-primary: var(--gray-100);
  --text-secondary: var(--gray-300);
  --text-tertiary: var(--gray-400);
  --border-primary: var(--gray-700);
  --border-secondary: var(--gray-600);
}

/* Light Theme Variables (Default) */
:root {
  --bg-primary: white;
  --bg-secondary: var(--gray-50);
  --bg-tertiary: var(--gray-100);
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-700);
  --text-tertiary: var(--gray-500);
  --border-primary: var(--gray-200);
  --border-secondary: var(--gray-300);
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-sans);
  font-size: 1rem;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Focus Management */
:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Skip Link for Accessibility */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-600);
  color: white;
  padding: var(--space-2) var(--space-4);
  text-decoration: none;
  border-radius: var(--radius-md);
  z-index: var(--z-tooltip);
  transition: top var(--transition-fast);
}

.skip-link:focus {
  top: 6px;
}

/* Visually Hidden (Screen Reader Only) */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

@media (min-width: 768px) {
  .container {
    padding: 0 var(--space-6);
  }
}

/* Header */
.header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-primary);
  padding: var(--space-4) 0;
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  backdrop-filter: blur(8px);
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

@media (min-width: 768px) {
  .header-content {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-600);
}

.logo-icon {
  font-size: 2rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

.tagline {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin: 0;
}

/* Status Indicator */
.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--success-500);
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.status-dot.error {
  background-color: var(--error-500);
}

.status-dot.warning {
  background-color: var(--warning-500);
}

/* Main Content */
.main {
  min-height: calc(100vh - 200px);
  padding: var(--space-6) 0;
}

/* Chat Interface */
.chat-interface {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
  min-height: 60vh;
}

/* Welcome Message */
.welcome-message {
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
  border: 1px solid var(--primary-200);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  text-align: center;
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.welcome-content h2 {
  color: var(--primary-700);
  margin-bottom: var(--space-4);
  font-size: 1.5rem;
}

.feature-list {
  list-style: none;
  display: grid;
  gap: var(--space-3);
  margin: var(--space-6) 0;
  text-align: left;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.feature-list li {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.welcome-cta {
  color: var(--text-secondary);
  font-weight: 500;
  margin-top: var(--space-4);
}

/* Chat History */
.chat-history {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  max-height: 500px;
  overflow-y: auto;
  padding: var(--space-4);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background: var(--bg-secondary);
}

.chat-history:empty {
  display: none;
}

/* Message Styles */
.message {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.message-header {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 0.875rem;
  color: var(--text-tertiary);
}

.message-content {
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  position: relative;
}

.user-message .message-content {
  background: var(--primary-500);
  color: white;
  margin-left: 20%;
  border-bottom-right-radius: var(--radius-sm);
}

.ai-message .message-content {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  margin-right: 20%;
  border-bottom-left-radius: var(--radius-sm);
  box-shadow: var(--shadow-sm);
}

/* Code Blocks */
.code-block {
  background: var(--gray-900);
  color: var(--gray-100);
  padding: var(--space-4);
  border-radius: var(--radius-md);
  font-family: var(--font-family-mono);
  font-size: 0.875rem;
  overflow-x: auto;
  margin: var(--space-3) 0;
  position: relative;
}

.code-block::before {
  content: attr(data-language);
  position: absolute;
  top: var(--space-2);
  right: var(--space-3);
  font-size: 0.75rem;
  color: var(--gray-400);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Input Section */
.input-section {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-lg);
  position: sticky;
  bottom: var(--space-4);
}

.input-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.input-wrapper {
  position: relative;
}

.question-input {
  width: 100%;
  min-height: 80px;
  padding: var(--space-4);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-family: inherit;
  font-size: 1rem;
  line-height: 1.5;
  resize: vertical;
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
  background: var(--bg-primary);
  color: var(--text-primary);
}

.question-input:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.question-input::placeholder {
  color: var(--text-tertiary);
}

.input-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--space-2);
  font-size: 0.875rem;
  color: var(--text-tertiary);
}

.char-counter {
  font-family: var(--font-family-mono);
}

.char-counter.warning {
  color: var(--warning-500);
}

.char-counter.error {
  color: var(--error-500);
}

.help-text {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.input-actions {
  display: flex;
  gap: var(--space-3);
  align-items: center;
}

.ask-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-6);
  background: var(--primary-500);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.ask-button:hover:not(:disabled) {
  background: var(--primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.ask-button:active {
  transform: translateY(0);
}

.ask-button:disabled {
  background: var(--gray-400);
  cursor: not-allowed;
  transform: none;
}

.clear-button {
  padding: var(--space-3);
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 1.2rem;
}

.clear-button:hover {
  background: var(--error-500);
  color: white;
  border-color: var(--error-500);
}

.shortcuts-help {
  text-align: center;
  color: var(--text-tertiary);
  margin-top: var(--space-2);
}

kbd {
  background: var(--gray-100);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-sm);
  padding: 0.125rem 0.25rem;
  font-size: 0.75rem;
  font-family: var(--font-family-mono);
}

/* Loading States */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  backdrop-filter: blur(4px);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.loading-overlay.visible {
  opacity: 1;
  visibility: visible;
}

.loading-content {
  background: var(--bg-primary);
  padding: var(--space-8);
  border-radius: var(--radius-xl);
  text-align: center;
  box-shadow: var(--shadow-xl);
  max-width: 300px;
  width: 90%;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray-200);
  border-top: 4px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--text-secondary);
  margin-bottom: var(--space-4);
  font-weight: 500;
}

.loading-progress {
  width: 100%;
  height: 4px;
  background: var(--gray-200);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: var(--primary-500);
  border-radius: 2px;
  animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
  0% { width: 0%; }
  50% { width: 70%; }
  100% { width: 100%; }
}

/* Footer */
.footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
  padding: var(--space-4) 0;
  margin-top: var(--space-12);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-4);
}

.footer-text {
  color: var(--text-tertiary);
  font-size: 0.875rem;
}

.footer-links {
  display: flex;
  gap: var(--space-4);
}

.footer-link {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  font-size: 0.875rem;
  padding: var(--space-2);
  border-radius: var(--radius-sm);
  transition: color var(--transition-fast);
}

.footer-link:hover {
  color: var(--primary-500);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-3);
  }
  
  .chat-interface {
    gap: var(--space-4);
  }
  
  .welcome-message {
    padding: var(--space-6);
  }
  
  .feature-list {
    grid-template-columns: 1fr;
  }
  
  .user-message .message-content {
    margin-left: 10%;
  }
  
  .ai-message .message-content {
    margin-right: 10%;
  }
  
  .input-section {
    padding: var(--space-4);
    border-radius: var(--radius-lg);
  }
  
  .input-actions {
    flex-direction: column;
  }
  
  .ask-button {
    width: 100%;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --border-primary: var(--gray-400);
    --border-secondary: var(--gray-500);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .loading-spinner {
    animation: none;
    border-top-color: var(--primary-500);
  }
}

/* Print Styles */
@media print {
  .header,
  .input-section,
  .footer,
  .loading-overlay {
    display: none;
  }
  
  .chat-history {
    max-height: none;
    overflow: visible;
    border: none;
    background: none;
  }
  
  .message-content {
    break-inside: avoid;
  }
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.modal.visible {
  opacity: 1;
  visibility: visible;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.modal-content {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  z-index: 1;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  color: var(--text-tertiary);
  transition: all var(--transition-fast);
}

.modal-close:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--space-6);
}

.setting-group {
  margin-bottom: var(--space-6);
}

.setting-group:last-child {
  margin-bottom: 0;
}

.setting-group label {
  display: block;
  margin-bottom: var(--space-2);
  font-weight: 500;
  color: var(--text-primary);
}

.setting-input {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 1rem;
  transition: border-color var(--transition-fast);
}

.setting-input:focus {
  border-color: var(--primary-500);
  outline: none;
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.setting-checkbox {
  margin-right: var(--space-2);
  accent-color: var(--primary-500);
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: var(--space-4);
  right: var(--space-4);
  z-index: var(--z-toast);
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  max-width: 400px;
  width: 100%;
}

.toast {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  animation: toastSlideIn 0.3s ease-out;
  position: relative;
  overflow: hidden;
}

@keyframes toastSlideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.toast.success {
  border-left: 4px solid var(--success-500);
}

.toast.warning {
  border-left: 4px solid var(--warning-500);
}

.toast.error {
  border-left: 4px solid var(--error-500);
}

.toast-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.toast-content {
  flex: 1;
}

.toast-title {
  font-weight: 600;
  margin-bottom: var(--space-1);
  color: var(--text-primary);
}

.toast-message {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.4;
}

.toast-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-sm);
  color: var(--text-tertiary);
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.toast-close:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background: var(--primary-500);
  animation: toastProgress 5s linear forwards;
}

@keyframes toastProgress {
  from { width: 100%; }
  to { width: 0%; }
}

/* Syntax Highlighting for Code Blocks */
.code-block .keyword { color: #ff79c6; }
.code-block .string { color: #f1fa8c; }
.code-block .comment { color: #6272a4; font-style: italic; }
.code-block .number { color: #bd93f9; }
.code-block .function { color: #50fa7b; }
.code-block .operator { color: #ff79c6; }
.code-block .variable { color: #8be9fd; }

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: 4px;
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--gray-400) var(--bg-secondary);
}

/* Selection Styling */
::selection {
  background: rgb(59 130 246 / 0.2);
  color: var(--text-primary);
}

/* Font Size Variations */
.font-small {
  font-size: 0.875rem;
}

.font-large {
  font-size: 1.125rem;
}

.font-large .question-input {
  font-size: 1.125rem;
}

.font-large .message-content {
  font-size: 1.125rem;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bounce {
  animation: bounce 0.6s ease-out;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.invisible {
  visibility: hidden;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.font-mono {
  font-family: var(--font-family-mono);
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-tertiary {
  color: var(--text-tertiary);
}

.bg-primary {
  background-color: var(--bg-primary);
}

.bg-secondary {
  background-color: var(--bg-secondary);
}

.border-primary {
  border-color: var(--border-primary);
}

/* Component States */
.loading {
  pointer-events: none;
  opacity: 0.6;
}

.disabled {
  pointer-events: none;
  opacity: 0.5;
}

.error {
  border-color: var(--error-500) !important;
  color: var(--error-500);
}

.success {
  border-color: var(--success-500) !important;
  color: var(--success-500);
}

.warning {
  border-color: var(--warning-500) !important;
  color: var(--warning-500);
}
