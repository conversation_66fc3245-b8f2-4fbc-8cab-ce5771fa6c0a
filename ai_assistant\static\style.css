/* 
Advanced AI Coding Assistant Styles
Modern, responsive design with accessibility and performance optimizations
*/

/*
🚀 REVOLUTIONARY DEV TERMINAL UI
Next-generation coding assistant interface
*/

/* CSS Custom Properties - Developer Terminal Theme */
:root {
  /* Terminal Color Palette - Inspired by VS Code Dark+ */
  --terminal-bg: #0d1117;
  --terminal-surface: #161b22;
  --terminal-elevated: #21262d;
  --terminal-border: #30363d;
  --terminal-accent: #58a6ff;
  --terminal-accent-bright: #79c0ff;
  --terminal-success: #3fb950;
  --terminal-warning: #d29922;
  --terminal-error: #f85149;
  --terminal-purple: #bc8cff;
  --terminal-cyan: #39d0d8;

  /* Text Colors */
  --text-primary: #f0f6fc;
  --text-secondary: #8b949e;
  --text-tertiary: #6e7681;
  --text-accent: #58a6ff;
  --text-success: #3fb950;
  --text-warning: #d29922;
  --text-error: #f85149;

  /* Syntax Highlighting */
  --syntax-keyword: #ff7b72;
  --syntax-string: #a5d6ff;
  --syntax-comment: #8b949e;
  --syntax-function: #d2a8ff;
  --syntax-variable: #ffa657;
  --syntax-number: #79c0ff;
  
  /* Typography - Developer Focused */
  --font-family-mono: 'JetBrains Mono', 'Fira Code', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  --font-family-sans: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-code: 'JetBrains Mono', 'Fira Code', monospace;
  
  /* Spacing Scale - Terminal Inspired */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-12: 3rem;
  --space-16: 4rem;

  /* Border Radius - Subtle, Professional */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  
  /* Shadows - Terminal Glow Effects */
  --shadow-terminal: 0 0 20px rgba(88, 166, 255, 0.1);
  --shadow-glow: 0 0 30px rgba(88, 166, 255, 0.2);
  --shadow-code: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  --shadow-depth: 0 8px 32px rgba(0, 0, 0, 0.3);

  /* Transitions - Snappy, Developer-Like */
  --transition-fast: 100ms cubic-bezier(0.23, 1, 0.32, 1);
  --transition-normal: 200ms cubic-bezier(0.23, 1, 0.32, 1);
  --transition-slow: 300ms cubic-bezier(0.23, 1, 0.32, 1);

  /* Animation Curves */
  --ease-out-expo: cubic-bezier(0.16, 1, 0.3, 1);
  --ease-in-out-circ: cubic-bezier(0.85, 0, 0.15, 1);
  
  /* Z-index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* Dark Theme Variables */
[data-theme="dark"] {
  --bg-primary: var(--gray-900);
  --bg-secondary: var(--gray-800);
  --bg-tertiary: var(--gray-700);
  --text-primary: var(--gray-100);
  --text-secondary: var(--gray-300);
  --text-tertiary: var(--gray-400);
  --border-primary: var(--gray-700);
  --border-secondary: var(--gray-600);
}

/* Light Theme Variables (Default) */
:root {
  --bg-primary: white;
  --bg-secondary: var(--gray-50);
  --bg-tertiary: var(--gray-100);
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-700);
  --text-tertiary: var(--gray-500);
  --border-primary: var(--gray-200);
  --border-secondary: var(--gray-300);
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-mono);
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-primary);
  background: var(--terminal-bg);
  background-image:
    radial-gradient(circle at 25% 25%, rgba(88, 166, 255, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(188, 140, 255, 0.05) 0%, transparent 50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  overflow-x: hidden;
  min-height: 100vh;
}

/* Focus Management */
:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Skip Link for Accessibility */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-600);
  color: white;
  padding: var(--space-2) var(--space-4);
  text-decoration: none;
  border-radius: var(--radius-md);
  z-index: var(--z-tooltip);
  transition: top var(--transition-fast);
}

.skip-link:focus {
  top: 6px;
}

/* Visually Hidden (Screen Reader Only) */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

@media (min-width: 768px) {
  .container {
    padding: 0 var(--space-6);
  }
}

/* Terminal Window Header */
.header {
  background: var(--terminal-surface);
  border-bottom: 1px solid var(--terminal-border);
  padding: var(--space-3) 0;
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  backdrop-filter: blur(12px);
  box-shadow: var(--shadow-terminal);

  /* Terminal window styling */
  &::before {
    content: '';
    position: absolute;
    top: var(--space-3);
    left: var(--space-4);
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--terminal-error);
    box-shadow:
      20px 0 0 var(--terminal-warning),
      40px 0 0 var(--terminal-success);
  }
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

@media (min-width: 768px) {
  .header-content {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-accent);
  font-family: var(--font-family-mono);
  margin-left: 80px; /* Space for terminal dots */

  &::before {
    content: '❯';
    color: var(--terminal-accent-bright);
    font-size: 1.2rem;
    animation: pulse-cursor 1.5s infinite;
  }
}

@keyframes pulse-cursor {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.logo-icon {
  font-size: 2rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

.tagline {
  color: var(--text-secondary);
  font-size: 0.75rem;
  margin: 0;
  font-family: var(--font-family-mono);
  font-style: italic;
  opacity: 0.8;
}

/* Status Indicator */
.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--success-500);
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.status-dot.error {
  background-color: var(--error-500);
}

.status-dot.warning {
  background-color: var(--warning-500);
}

/* Main Content */
.main {
  min-height: calc(100vh - 200px);
  padding: var(--space-6) 0;
}

/* Terminal Chat Interface */
.chat-interface {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  min-height: 80vh;
  background: var(--terminal-surface);
  border: 1px solid var(--terminal-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-depth);
  overflow: hidden;
  position: relative;

  /* Terminal scanlines effect */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
      0deg,
      transparent,
      transparent 2px,
      rgba(88, 166, 255, 0.02) 2px,
      rgba(88, 166, 255, 0.02) 4px
    );
    pointer-events: none;
    z-index: 1;
  }
}

/* Terminal Startup Message */
.welcome-message {
  background: var(--terminal-elevated);
  border: 1px solid var(--terminal-border);
  border-radius: var(--radius-md);
  padding: var(--space-6);
  margin: var(--space-4);
  position: relative;
  z-index: 2;

  /* Terminal prompt styling */
  &::before {
    content: '$ ./ai-assistant --init';
    display: block;
    color: var(--terminal-accent);
    font-family: var(--font-family-mono);
    font-size: 0.875rem;
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-2);
    border-bottom: 1px solid var(--terminal-border);
  }

  animation: terminalBoot 1.2s ease-out;
}

@keyframes terminalBoot {
  0% {
    opacity: 0;
    transform: translateY(10px);
    filter: blur(1px);
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
    transform: translateY(0);
    filter: blur(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.welcome-content h2 {
  color: var(--text-accent);
  margin-bottom: var(--space-4);
  font-size: 1.125rem;
  font-family: var(--font-family-mono);
  font-weight: 600;
}

.feature-list {
  list-style: none;
  display: grid;
  gap: var(--space-2);
  margin: var(--space-4) 0;
  text-align: left;
  font-family: var(--font-family-mono);
  font-size: 0.8rem;
}

.feature-list li {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-2) var(--space-3);
  background: var(--terminal-bg);
  border: 1px solid var(--terminal-border);
  border-radius: var(--radius-sm);
  color: var(--text-secondary);
  transition: all var(--transition-fast);

  &::before {
    content: '▸';
    color: var(--terminal-success);
    font-weight: bold;
  }

  &:hover {
    background: var(--terminal-elevated);
    border-color: var(--terminal-accent);
    color: var(--text-primary);
    transform: translateX(4px);
  }
}

.welcome-cta {
  color: var(--text-secondary);
  font-weight: 500;
  margin-top: var(--space-4);
}

/* Terminal Chat History */
.chat-history {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  max-height: 600px;
  overflow-y: auto;
  padding: var(--space-4);
  background: var(--terminal-bg);
  position: relative;
  z-index: 2;

  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--terminal-surface);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--terminal-border);
    border-radius: 4px;

    &:hover {
      background: var(--terminal-accent);
    }
  }
}

.chat-history:empty {
  display: none;
}

/* Terminal Message Styles */
.message {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  animation: terminalType 0.4s ease-out;
  font-family: var(--font-family-mono);
  position: relative;
}

@keyframes terminalType {
  0% {
    opacity: 0;
    transform: translateY(8px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.message-header {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 0.75rem;
  color: var(--text-tertiary);
  font-family: var(--font-family-mono);
  margin-bottom: var(--space-1);
}

.message-content {
  padding: var(--space-3);
  border-radius: var(--radius-md);
  position: relative;
  font-size: 0.875rem;
  line-height: 1.6;
}

/* User Command Styling */
.user-message .message-content {
  background: var(--terminal-elevated);
  border: 1px solid var(--terminal-accent);
  border-left: 3px solid var(--terminal-accent);
  color: var(--text-primary);

  &::before {
    content: '$ ';
    color: var(--terminal-accent);
    font-weight: bold;
  }
}

/* AI Response Styling */
.ai-message .message-content {
  background: var(--terminal-surface);
  border: 1px solid var(--terminal-border);
  border-left: 3px solid var(--terminal-success);
  color: var(--text-primary);
  box-shadow: var(--shadow-code);

  &::before {
    content: '❯ ';
    color: var(--terminal-success);
    font-weight: bold;
  }
}

/* Advanced Code Blocks */
.code-block {
  background: var(--terminal-bg);
  border: 1px solid var(--terminal-border);
  border-left: 3px solid var(--terminal-purple);
  color: var(--text-primary);
  padding: var(--space-4);
  border-radius: var(--radius-md);
  font-family: var(--font-family-mono);
  font-size: 0.8rem;
  overflow-x: auto;
  margin: var(--space-3) 0;
  position: relative;
  box-shadow: var(--shadow-code);

  /* Terminal header */
  &::before {
    content: attr(data-language) ' // code';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background: var(--terminal-surface);
    border-bottom: 1px solid var(--terminal-border);
    padding: var(--space-2) var(--space-3);
    font-size: 0.7rem;
    color: var(--text-tertiary);
    font-family: var(--font-family-mono);
  }

  /* Code content */
  pre, code {
    margin-top: var(--space-6);
    color: var(--text-primary);
  }
}

/* Terminal Input Section */
.input-section {
  background: var(--terminal-surface);
  border: 1px solid var(--terminal-border);
  border-top: 2px solid var(--terminal-accent);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  box-shadow: var(--shadow-terminal);
  position: sticky;
  bottom: var(--space-4);
  margin: var(--space-4);
  z-index: 10;

  /* Terminal glow effect */
  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
      var(--terminal-accent),
      var(--terminal-purple),
      var(--terminal-cyan),
      var(--terminal-accent)
    );
    background-size: 200% 100%;
    animation: terminalGlow 3s ease-in-out infinite;
  }
}

@keyframes terminalGlow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.input-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.input-wrapper {
  position: relative;
}

.question-input {
  width: 100%;
  min-height: 100px;
  padding: var(--space-4);
  padding-left: var(--space-6);
  border: 1px solid var(--terminal-border);
  border-radius: var(--radius-md);
  font-family: var(--font-family-mono);
  font-size: 0.875rem;
  line-height: 1.6;
  resize: vertical;
  transition: all var(--transition-normal);
  background: var(--terminal-bg);
  color: var(--text-primary);
  position: relative;

  /* Terminal prompt indicator */
  background-image: linear-gradient(to right, var(--terminal-accent) 0%, var(--terminal-accent) 2px, transparent 2px);
  background-repeat: no-repeat;
  background-position: var(--space-2) var(--space-4);

  &:focus {
    border-color: var(--terminal-accent);
    box-shadow: 0 0 0 2px rgba(88, 166, 255, 0.2);
    outline: none;
    background-image: linear-gradient(to right, var(--terminal-accent-bright) 0%, var(--terminal-accent-bright) 2px, transparent 2px);
  }

  &::placeholder {
    color: var(--text-tertiary);
    font-style: italic;
  }
}

.input-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--space-2);
  font-size: 0.875rem;
  color: var(--text-tertiary);
}

.char-counter {
  font-family: var(--font-family-mono);
}

.char-counter.warning {
  color: var(--warning-500);
}

.char-counter.error {
  color: var(--error-500);
}

.help-text {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.input-actions {
  display: flex;
  gap: var(--space-3);
  align-items: center;
}

.ask-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  background: var(--terminal-elevated);
  color: var(--terminal-accent);
  border: 1px solid var(--terminal-accent);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 600;
  font-family: var(--font-family-mono);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(88, 166, 255, 0.2), transparent);
    transition: left var(--transition-normal);
  }

  &:hover:not(:disabled) {
    background: var(--terminal-accent);
    color: var(--terminal-bg);
    box-shadow: 0 0 20px rgba(88, 166, 255, 0.3);
    transform: translateY(-1px);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    background: var(--terminal-surface);
    color: var(--text-tertiary);
    border-color: var(--terminal-border);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}

.clear-button {
  padding: var(--space-3);
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: 1.2rem;
}

.clear-button:hover {
  background: var(--error-500);
  color: white;
  border-color: var(--error-500);
}

.shortcuts-help {
  text-align: center;
  color: var(--text-tertiary);
  margin-top: var(--space-2);
}

kbd {
  background: var(--gray-100);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-sm);
  padding: 0.125rem 0.25rem;
  font-size: 0.75rem;
  font-family: var(--font-family-mono);
}

/* Loading States */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  backdrop-filter: blur(4px);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.loading-overlay.visible {
  opacity: 1;
  visibility: visible;
}

.loading-content {
  background: var(--bg-primary);
  padding: var(--space-8);
  border-radius: var(--radius-xl);
  text-align: center;
  box-shadow: var(--shadow-xl);
  max-width: 300px;
  width: 90%;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray-200);
  border-top: 4px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--text-secondary);
  margin-bottom: var(--space-4);
  font-weight: 500;
}

.loading-progress {
  width: 100%;
  height: 4px;
  background: var(--gray-200);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: var(--primary-500);
  border-radius: 2px;
  animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
  0% { width: 0%; }
  50% { width: 70%; }
  100% { width: 100%; }
}

/* Footer */
.footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
  padding: var(--space-4) 0;
  margin-top: var(--space-12);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-4);
}

.footer-text {
  color: var(--text-tertiary);
  font-size: 0.875rem;
}

.footer-links {
  display: flex;
  gap: var(--space-4);
}

.footer-link {
  background: none;
  border: none;
  color: var(--text-tertiary);
  cursor: pointer;
  font-size: 0.875rem;
  padding: var(--space-2);
  border-radius: var(--radius-sm);
  transition: color var(--transition-fast);
}

.footer-link:hover {
  color: var(--primary-500);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-3);
  }
  
  .chat-interface {
    gap: var(--space-4);
  }
  
  .welcome-message {
    padding: var(--space-6);
  }
  
  .feature-list {
    grid-template-columns: 1fr;
  }
  
  .user-message .message-content {
    margin-left: 10%;
  }
  
  .ai-message .message-content {
    margin-right: 10%;
  }
  
  .input-section {
    padding: var(--space-4);
    border-radius: var(--radius-lg);
  }
  
  .input-actions {
    flex-direction: column;
  }
  
  .ask-button {
    width: 100%;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --border-primary: var(--gray-400);
    --border-secondary: var(--gray-500);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .loading-spinner {
    animation: none;
    border-top-color: var(--primary-500);
  }
}

/* Print Styles */
@media print {
  .header,
  .input-section,
  .footer,
  .loading-overlay {
    display: none;
  }
  
  .chat-history {
    max-height: none;
    overflow: visible;
    border: none;
    background: none;
  }
  
  .message-content {
    break-inside: avoid;
  }
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.modal.visible {
  opacity: 1;
  visibility: visible;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.modal-content {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  z-index: 1;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  color: var(--text-tertiary);
  transition: all var(--transition-fast);
}

.modal-close:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.modal-body {
  padding: var(--space-6);
}

.setting-group {
  margin-bottom: var(--space-6);
}

.setting-group:last-child {
  margin-bottom: 0;
}

.setting-group label {
  display: block;
  margin-bottom: var(--space-2);
  font-weight: 500;
  color: var(--text-primary);
}

.setting-input {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 1rem;
  transition: border-color var(--transition-fast);
}

.setting-input:focus {
  border-color: var(--primary-500);
  outline: none;
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.setting-checkbox {
  margin-right: var(--space-2);
  accent-color: var(--primary-500);
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: var(--space-4);
  right: var(--space-4);
  z-index: var(--z-toast);
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  max-width: 400px;
  width: 100%;
}

.toast {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  animation: toastSlideIn 0.3s ease-out;
  position: relative;
  overflow: hidden;
}

@keyframes toastSlideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.toast.success {
  border-left: 4px solid var(--success-500);
}

.toast.warning {
  border-left: 4px solid var(--warning-500);
}

.toast.error {
  border-left: 4px solid var(--error-500);
}

.toast-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.toast-content {
  flex: 1;
}

.toast-title {
  font-weight: 600;
  margin-bottom: var(--space-1);
  color: var(--text-primary);
}

.toast-message {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.4;
}

.toast-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-1);
  border-radius: var(--radius-sm);
  color: var(--text-tertiary);
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.toast-close:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background: var(--primary-500);
  animation: toastProgress 5s linear forwards;
}

@keyframes toastProgress {
  from { width: 100%; }
  to { width: 0%; }
}

/* Syntax Highlighting for Code Blocks */
.code-block .keyword { color: #ff79c6; }
.code-block .string { color: #f1fa8c; }
.code-block .comment { color: #6272a4; font-style: italic; }
.code-block .number { color: #bd93f9; }
.code-block .function { color: #50fa7b; }
.code-block .operator { color: #ff79c6; }
.code-block .variable { color: #8be9fd; }

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: 4px;
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--gray-400) var(--bg-secondary);
}

/* Selection Styling */
::selection {
  background: rgb(59 130 246 / 0.2);
  color: var(--text-primary);
}

/* Font Size Variations */
.font-small {
  font-size: 0.875rem;
}

.font-large {
  font-size: 1.125rem;
}

.font-large .question-input {
  font-size: 1.125rem;
}

.font-large .message-content {
  font-size: 1.125rem;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bounce {
  animation: bounce 0.6s ease-out;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.invisible {
  visibility: hidden;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.font-mono {
  font-family: var(--font-family-mono);
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-tertiary {
  color: var(--text-tertiary);
}

.bg-primary {
  background-color: var(--bg-primary);
}

.bg-secondary {
  background-color: var(--bg-secondary);
}

.border-primary {
  border-color: var(--border-primary);
}

/* Component States */
.loading {
  pointer-events: none;
  opacity: 0.6;
}

.disabled {
  pointer-events: none;
  opacity: 0.5;
}

.error {
  border-color: var(--error-500) !important;
  color: var(--error-500);
}

.success {
  border-color: var(--success-500) !important;
  color: var(--success-500);
}

.warning {
  border-color: var(--terminal-warning) !important;
  color: var(--terminal-warning);
}

/* Advanced Terminal Effects */
@keyframes dataStream {
  0% { transform: translateY(100vh) rotate(180deg); opacity: 0; }
  10% { opacity: 0.3; }
  90% { opacity: 0.3; }
  100% { transform: translateY(-100vh) rotate(180deg); opacity: 0; }
}

.terminal-particle {
  position: fixed;
  width: 2px;
  height: 20px;
  background: linear-gradient(to bottom, transparent, var(--terminal-accent), transparent);
  pointer-events: none;
  z-index: 0;
  animation: dataStream 8s linear infinite;
}

/* Matrix-style background effect */
.matrix-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  opacity: 0.03;
  background-image:
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 20px,
      var(--terminal-accent) 20px,
      var(--terminal-accent) 21px
    ),
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 20px,
      var(--terminal-accent) 20px,
      var(--terminal-accent) 21px
    );
}

/* Typing indicator animation */
.typing-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.typing-indicator::after {
  content: '';
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: var(--terminal-accent);
  animation: typingPulse 1.5s infinite;
}

@keyframes typingPulse {
  0%, 60%, 100% { opacity: 0; }
  30% { opacity: 1; }
}

/* Enhanced focus states */
*:focus-visible {
  outline: 2px solid var(--terminal-accent);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Terminal cursor blink */
.terminal-cursor::after {
  content: '▋';
  color: var(--terminal-accent);
  animation: cursorBlink 1s infinite;
}

@keyframes cursorBlink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}
