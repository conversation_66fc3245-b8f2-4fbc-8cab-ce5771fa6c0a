#!/usr/bin/env python3
"""
AI Coding Assistant - Application Runner
Handles async initialization and provides a clean startup process
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from dotenv import load_dotenv
    import httpx
    from app import create_app
    from agents import AIAgentSystem, AIAgentDependencies
except ImportError as e:
    print(f"❌ Missing required dependency: {e}")
    print("📦 Please install requirements: pip install -r requirements.txt")
    sys.exit(1)


async def initialize_ai_system(app):
    """Initialize the AI system asynchronously"""
    try:
        print("🤖 Initializing AI system...")
        
        # Create HTTP client
        http_client = httpx.AsyncClient(
            timeout=httpx.Timeout(30.0),
            limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)
        )
        
        # Create dependencies
        deps = AIAgentDependencies(
            api_key=app.config['OPENROUTER_API_KEY'],
            http_client=http_client,
            rate_limit_per_minute=app.config['MAX_REQUESTS_PER_MINUTE'],
            max_tokens=app.config['MAX_TOKENS_PER_REQUEST']
        )
        
        # Create AI agent system
        ai_agent = AIAgentSystem(deps)
        
        # Store in app context
        app.ai_agent = ai_agent
        app.http_client = http_client
        
        print("✅ AI system initialized successfully")
        return True
        
    except Exception as e:
        print(f"❌ Failed to initialize AI system: {e}")
        return False


def run_async_in_thread(coro):
    """Run async function in a separate thread"""
    def run():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro)
        finally:
            loop.close()
    
    import threading
    thread = threading.Thread(target=run)
    thread.start()
    thread.join()


def main():
    """Main application entry point"""
    print("🚀 Starting AI Coding Assistant...")
    
    # Load environment variables
    load_dotenv()
    
    # Validate required environment variables
    api_key = os.getenv('OPENROUTER_API_KEY')
    if not api_key or api_key == 'your_openrouter_api_key_here':
        print("❌ Error: OPENROUTER_API_KEY not set!")
        print("📝 Please set your OpenRouter API key in the .env file")
        print("🔗 Get your API key from: https://openrouter.ai/keys")
        sys.exit(1)
    
    try:
        # Create Flask app
        app = create_app()
        
        # Initialize AI system
        print("🔧 Setting up AI system...")
        
        # Simple sync initialization for now
        app.ai_agent = None
        app.http_client = None
        
        # Get configuration
        host = os.getenv('HOST', '0.0.0.0')
        port = int(os.getenv('PORT', 5000))
        debug = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
        
        print(f"🌐 Server starting on http://{host}:{port}")
        print(f"🔧 Debug mode: {'ON' if debug else 'OFF'}")
        print("📖 Visit the URL above to start using the AI assistant!")
        print("⏹️  Press Ctrl+C to stop the server")
        
        # Run the Flask app
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 Shutting down gracefully...")
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        sys.exit(1)
    finally:
        print("🛑 Server stopped")


if __name__ == '__main__':
    main()
