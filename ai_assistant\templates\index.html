<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Advanced AI Coding Assistant - Get expert programming help with modern best practices">
    <meta name="keywords" content="AI, coding, programming, assistant, help, development">
    <title>AI Coding Assistant - Expert Programming Help</title>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="{{ url_for('static', filename='style.css') }}" as="style">
    <link rel="preload" href="{{ url_for('static', filename='app.js') }}" as="script">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    
    <!-- Favicon and app icons -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🤖</text></svg>">
    
    <!-- Theme color for mobile browsers -->
    <meta name="theme-color" content="#2563eb">
    
    <!-- Open Graph meta tags -->
    <meta property="og:title" content="AI Coding Assistant">
    <meta property="og:description" content="Get expert programming help with modern best practices">
    <meta property="og:type" content="website">
</head>
<body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Terminal background effects -->
    <div class="matrix-bg" aria-hidden="true"></div>
    
    <!-- Header -->
    <header class="header" role="banner">
        <div class="container">
            <div class="header-content">
                <h1 class="logo">
                    DeepSeek AI Terminal
                </h1>
                <p class="tagline">// Advanced coding intelligence at your fingertips</p>
            </div>
            
            <!-- Status indicator -->
            <div class="status-indicator" id="status-indicator" aria-live="polite">
                <span class="status-dot" id="status-dot"></span>
                <span class="status-text" id="status-text">Ready</span>
            </div>
        </div>
    </header>

    <!-- Main content -->
    <main id="main-content" class="main" role="main">
        <div class="container">
            <!-- Chat interface -->
            <div class="chat-interface">
                <!-- Welcome message -->
                <div class="welcome-message" id="welcome-message">
                    <div class="welcome-content">
                        <h2>DeepSeek AI Terminal v3.0 - Ready</h2>
                        <p>// Initializing advanced coding intelligence...</p>
                        <ul class="feature-list">
                            <li>Code analysis & debugging protocols</li>
                            <li>Real-time code generation engine</li>
                            <li>Architecture pattern recognition</li>
                            <li>Best practice enforcement system</li>
                            <li>Multi-language framework support</li>
                            <li>Performance optimization algorithms</li>
                        </ul>
                        <p class="welcome-cta">// Ready for input. Type your query below...</p>
                    </div>
                </div>
                
                <!-- Chat history -->
                <div class="chat-history" id="chat-history" role="log" aria-live="polite" aria-label="Conversation history">
                    <!-- Messages will be dynamically added here -->
                </div>
                
                <!-- Input section -->
                <div class="input-section">
                    <form class="input-form" id="question-form" role="form" aria-label="Ask a question">
                        <div class="input-wrapper">
                            <label for="question-input" class="visually-hidden">Enter your programming question</label>
                            <textarea
                                id="question-input"
                                class="question-input"
                                placeholder="Enter your coding query... (e.g., 'implement jwt auth in python')"
                                rows="3"
                                maxlength="5000"
                                required
                                aria-describedby="input-help"
                            ></textarea>
                            
                            <!-- Character counter -->
                            <div class="input-meta">
                                <span class="char-counter" id="char-counter" aria-live="polite">0 / 5000</span>
                                <div class="input-help" id="input-help">
                                    <span class="help-text">// Tip: Specific queries yield optimal results</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="input-actions">
                            <button 
                                type="submit" 
                                class="ask-button" 
                                id="ask-button"
                                aria-describedby="button-help"
                            >
                                <span class="button-text">Execute Query</span>
                                <span class="button-icon" aria-hidden="true">▶</span>
                            </button>
                            
                            <button 
                                type="button" 
                                class="clear-button" 
                                id="clear-button"
                                title="Clear conversation"
                                aria-label="Clear conversation history"
                            >
                                <span aria-hidden="true">🗑️</span>
                            </button>
                        </div>
                    </form>
                    
                    <!-- Keyboard shortcuts help -->
                    <div class="shortcuts-help" id="shortcuts-help">
                        <small>
                            <kbd>Ctrl</kbd> + <kbd>Enter</kbd> to execute •
                            <kbd>Shift</kbd> + <kbd>Enter</kbd> for new line
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Loading indicator -->
            <div class="loading-overlay" id="loading-overlay" aria-hidden="true">
                <div class="loading-content">
                    <div class="loading-spinner" aria-hidden="true"></div>
                    <p class="loading-text" id="loading-text">Thinking...</p>
                    <div class="loading-progress">
                        <div class="progress-bar" id="progress-bar"></div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <p class="footer-text">
                    Powered by advanced AI models • 
                    <span id="session-info">Session: {{ session_id[:8] }}...</span>
                </p>
                <div class="footer-links">
                    <button class="footer-link" id="export-button" title="Export conversation">
                        📥 Export
                    </button>
                    <button class="footer-link" id="settings-button" title="Settings">
                        ⚙️ Settings
                    </button>
                </div>
            </div>
        </div>
    </footer>

    <!-- Settings modal -->
    <div class="modal" id="settings-modal" aria-hidden="true" role="dialog" aria-labelledby="settings-title">
        <div class="modal-overlay" id="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="settings-title">Settings</h3>
                <button class="modal-close" id="modal-close" aria-label="Close settings">×</button>
            </div>
            <div class="modal-body">
                <div class="setting-group">
                    <label for="theme-select">Theme</label>
                    <select id="theme-select" class="setting-input">
                        <option value="auto">Auto (System)</option>
                        <option value="light">Light</option>
                        <option value="dark">Dark</option>
                    </select>
                </div>
                
                <div class="setting-group">
                    <label for="font-size-select">Font Size</label>
                    <select id="font-size-select" class="setting-input">
                        <option value="small">Small</option>
                        <option value="medium" selected>Medium</option>
                        <option value="large">Large</option>
                    </select>
                </div>
                
                <div class="setting-group">
                    <label>
                        <input type="checkbox" id="sound-enabled" class="setting-checkbox">
                        Enable sound notifications
                    </label>
                </div>
                
                <div class="setting-group">
                    <label>
                        <input type="checkbox" id="auto-scroll" class="setting-checkbox" checked>
                        Auto-scroll to new messages
                    </label>
                </div>
            </div>
        </div>
    </div>

    <!-- Error toast container -->
    <div class="toast-container" id="toast-container" aria-live="assertive" aria-atomic="true">
        <!-- Toast messages will be dynamically added here -->
    </div>

    <!-- Hidden data for JavaScript -->
    <script type="application/json" id="app-config">
        {
            "sessionId": "{{ session_id }}",
            "apiEndpoint": "{{ url_for('ask_ai') }}",
            "historyEndpoint": "{{ url_for('get_history') }}",
            "maxMessageLength": 5000,
            "version": "1.0.0"
        }
    </script>

    <!-- Main application script -->
    <script src="{{ url_for('static', filename='app.js') }}" defer></script>
    
    <!-- Service worker registration disabled for now -->
    <!-- <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => console.log('SW registered'))
                    .catch(error => console.log('SW registration failed'));
            });
        }
    </script> -->
</body>
</html>
