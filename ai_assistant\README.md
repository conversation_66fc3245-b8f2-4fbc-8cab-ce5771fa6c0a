# 🤖 AI Coding Assistant

A production-ready, advanced AI coding assistant built with Flask, Pydantic AI, and modern web technologies. Get expert programming help with intelligent model routing, structured responses, and comprehensive error handling.

## ✨ Features

### 🧠 Advanced AI Capabilities
- **Intelligent Model Routing**: Automatically selects the best AI model for each query type
- **Structured Responses**: Rich, formatted responses with code snippets, best practices, and resources
- **Multi-Model Fallback**: Seamless fallback between OpenAI, Anthropic, Google, and Meta models
- **Context-Aware**: Maintains conversation context and provides relevant follow-up suggestions

### 🎨 Modern User Interface
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Dark/Light Theme**: Automatic theme switching based on system preferences
- **Accessibility First**: Full keyboard navigation, screen reader support, and WCAG compliance
- **Real-time Feedback**: Live character counting, typing indicators, and progress bars

### 🔧 Developer Experience
- **Type Safety**: Full TypeScript-like validation with Pydantic models
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Rate Limiting**: Built-in rate limiting to prevent abuse
- **Conversation Export**: Export conversations in JSON format
- **Settings Persistence**: User preferences saved locally

### 🚀 Performance & Reliability
- **Async Processing**: Non-blocking request handling with httpx
- **Caching**: Intelligent caching for improved response times
- **Retry Logic**: Automatic retry with exponential backoff
- **Health Monitoring**: Built-in health check endpoints

## 🛠️ Technology Stack

- **Backend**: Flask 3.0+ with async support
- **AI Framework**: Pydantic AI with OpenRouter integration
- **Frontend**: Vanilla JavaScript with modern ES6+ features
- **Styling**: CSS Grid/Flexbox with CSS custom properties
- **Validation**: Pydantic v2 for request/response validation
- **HTTP Client**: httpx for async HTTP requests

## 📋 Prerequisites

- Python 3.9 or higher
- OpenRouter API key ([Get one here](https://openrouter.ai/keys))
- Modern web browser with JavaScript enabled

## 🚀 Quick Start

### 1. Clone and Setup

```bash
# Clone the repository
git clone <repository-url>
cd ai_assistant

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your settings
# At minimum, set your OpenRouter API key:
OPENROUTER_API_KEY=your_api_key_here
```

### 3. Run the Application

```bash
# Development mode
python app.py

# Or using Flask CLI
flask run

# Production mode (with gunicorn)
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### 4. Access the Application

Open your browser and navigate to:
- Development: http://localhost:5000
- Health Check: http://localhost:5000/health

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `OPENROUTER_API_KEY` | Your OpenRouter API key | Required |
| `SECRET_KEY` | Flask secret key for sessions | Auto-generated |
| `FLASK_DEBUG` | Enable debug mode | `False` |
| `MAX_REQUESTS_PER_MINUTE` | Rate limit per IP | `30` |
| `MAX_TOKENS_PER_REQUEST` | Max tokens per AI request | `4000` |
| `SESSION_TIMEOUT_MINUTES` | Session timeout | `60` |

### AI Model Configuration

The application supports multiple AI providers with intelligent fallback:

**Primary Models** (tried first):
- OpenAI GPT-4o
- Anthropic Claude 3.5 Sonnet
- Google Gemini 2.0 Flash

**Fallback Models** (if primary fails):
- OpenAI GPT-4o Mini
- Anthropic Claude 3.5 Haiku
- Meta Llama 3.3 70B

## 🏗️ Architecture

### Backend Structure

```
ai_assistant/
├── app.py              # Main Flask application
├── agents.py           # AI agent system with Pydantic AI
├── templates/
│   └── index.html      # Main application template
├── static/
│   ├── style.css       # Modern CSS with custom properties
│   └── app.js          # Advanced JavaScript application
├── data/
│   └── history.json    # Conversation history storage
└── .env                # Environment configuration
```

### Key Components

1. **AIAgentSystem**: Manages multiple AI agents with intelligent routing
2. **Flask Application**: Handles HTTP requests with comprehensive error handling
3. **Pydantic Models**: Ensures type safety for all API interactions
4. **JavaScript App**: Modern client-side application with full functionality

## 🔌 API Endpoints

### POST /ask
Submit a question to the AI assistant.

**Request:**
```json
{
  "question": "How do I implement authentication in Flask?",
  "context": {},
  "session_id": "optional-session-id"
}
```

**Response:**
```json
{
  "answer": {
    "explanation": "To implement authentication in Flask...",
    "code_snippet": "from flask_login import LoginManager...",
    "best_practices": ["Use secure session management", "..."],
    "confidence_score": 0.95
  },
  "session_id": "session-uuid",
  "timestamp": "2024-01-01T12:00:00Z",
  "processing_time_ms": 1500
}
```

### GET /history
Retrieve conversation history for the current session.

### GET /health
Health check endpoint for monitoring.

## 🎯 Usage Examples

### Basic Programming Question
```
Q: "How do I create a REST API in Python?"
A: Comprehensive explanation with Flask/FastAPI examples, best practices, and security considerations.
```

### Debugging Help
```
Q: "I'm getting a 'KeyError' in my Python dictionary. How do I fix this?"
A: Detailed debugging steps, prevention techniques, and code examples.
```

### Architecture Advice
```
Q: "What's the best way to structure a large Flask application?"
A: Blueprint patterns, project organization, and scalability recommendations.
```

## 🔧 Development

### Running Tests

```bash
# Install test dependencies
pip install pytest pytest-asyncio pytest-cov

# Run tests
pytest

# Run with coverage
pytest --cov=. --cov-report=html
```

### Code Quality

```bash
# Format code
black .

# Lint code
flake8 .

# Type checking
mypy .
```

### Development Mode

```bash
# Enable debug mode
export FLASK_DEBUG=True

# Auto-reload templates
export TEMPLATES_AUTO_RELOAD=True

# Run development server
python app.py
```

## 🚀 Deployment

### Production Checklist

- [ ] Set `FLASK_DEBUG=False`
- [ ] Use a strong `SECRET_KEY`
- [ ] Configure proper rate limiting
- [ ] Set up HTTPS/SSL
- [ ] Configure logging
- [ ] Set up monitoring
- [ ] Use a production WSGI server (gunicorn/uwsgi)

### Docker Deployment

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "app:app"]
```

### Environment-Specific Configs

```bash
# Development
FLASK_DEBUG=True
LOG_LEVEL=DEBUG

# Production
FLASK_DEBUG=False
LOG_LEVEL=WARNING
FORCE_HTTPS=True
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Pydantic AI](https://ai.pydantic.dev/) for the excellent AI framework
- [OpenRouter](https://openrouter.ai/) for unified AI model access
- [Flask](https://flask.palletsprojects.com/) for the web framework
- The open-source community for inspiration and tools

## 📞 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/example)
- 📖 Documentation: [Full docs](https://docs.example.com)
- 🐛 Issues: [GitHub Issues](https://github.com/example/ai-assistant/issues)

---

**Built with ❤️ for developers, by developers.**
