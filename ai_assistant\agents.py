"""
Advanced AI Agent System using Pydantic AI
Implements production-ready patterns with comprehensive error handling,
structured outputs, and intelligent model routing.
"""

import asyncio
import logging
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from enum import Enum

from pydantic import BaseModel, Field, ValidationError
from pydantic_ai import Agent, ModelRetry, RunContext
from pydantic_ai.exceptions import UnexpectedModelBehavior, UsageLimitExceeded
from pydantic_ai.usage import UsageLimits
import httpx

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ModelProvider(str, Enum):
    """Supported AI model providers with fallback hierarchy"""
    DEEPSEEK_CHAT = "deepseek/deepseek-chat-v3-0324:free"
    OPENAI_GPT4O = "openai/gpt-4o"
    OPENAI_GPT4O_MINI = "openai/gpt-4o-mini"
    ANTHROPIC_CLAUDE = "anthropic/claude-3-5-sonnet-latest"
    ANTHROPIC_HAIKU = "anthropic/claude-3-5-haiku-latest"
    GOOGLE_GEMINI = "google/gemini-2.0-flash-exp"
    META_LLAMA = "meta-llama/llama-3.3-70b-instruct"


class ResponseType(str, Enum):
    """Types of responses the AI can provide"""
    CODE_EXPLANATION = "code_explanation"
    CODE_GENERATION = "code_generation"
    DEBUGGING_HELP = "debugging_help"
    GENERAL_PROGRAMMING = "general_programming"
    ARCHITECTURE_ADVICE = "architecture_advice"


class CodeLanguage(str, Enum):
    """Supported programming languages"""
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    JAVA = "java"
    CSHARP = "csharp"
    CPP = "cpp"
    RUST = "rust"
    GO = "go"
    OTHER = "other"


@dataclass
class AIAgentDependencies:
    """Dependencies injected into the AI agent"""
    api_key: str
    http_client: httpx.AsyncClient
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    rate_limit_per_minute: int = 60
    max_tokens: int = 4000


class CodingResponse(BaseModel):
    """Structured response for coding-related queries"""
    response_type: ResponseType = Field(description="Type of coding response")
    language: Optional[CodeLanguage] = Field(None, description="Programming language if applicable")
    explanation: str = Field(description="Main explanation or answer")
    code_snippet: Optional[str] = Field(None, description="Code example if applicable")
    best_practices: List[str] = Field(default_factory=list, description="Relevant best practices")
    additional_resources: List[str] = Field(default_factory=list, description="Helpful links or resources")
    confidence_score: float = Field(ge=0.0, le=1.0, description="Confidence in the response (0-1)")
    follow_up_suggestions: List[str] = Field(default_factory=list, description="Suggested follow-up questions")


class ErrorResponse(BaseModel):
    """Structured error response"""
    error_type: str = Field(description="Type of error encountered")
    message: str = Field(description="Human-readable error message")
    suggestion: Optional[str] = Field(None, description="Suggestion for resolving the error")
    retry_after: Optional[int] = Field(None, description="Seconds to wait before retrying")


class AIAgentSystem:
    """
    Advanced AI Agent System with intelligent routing, fallbacks, and structured outputs
    """
    
    def __init__(self, dependencies: AIAgentDependencies):
        self.deps = dependencies
        self.primary_models = [
            ModelProvider.DEEPSEEK_CHAT,
            ModelProvider.OPENAI_GPT4O_MINI,
            ModelProvider.ANTHROPIC_HAIKU
        ]
        self.fallback_models = [
            ModelProvider.OPENAI_GPT4O,
            ModelProvider.ANTHROPIC_CLAUDE,
            ModelProvider.GOOGLE_GEMINI,
            ModelProvider.META_LLAMA
        ]
        
        # Initialize the main coding assistant agent
        self.coding_agent = self._create_coding_agent()
        
        # Initialize specialized agents
        self.debug_agent = self._create_debug_agent()
        self.architecture_agent = self._create_architecture_agent()
    
    def _create_coding_agent(self) -> Agent:
        """Create the main coding assistant agent with advanced capabilities"""
        agent = Agent(
            model=ModelProvider.DEEPSEEK_CHAT.value,
            deps_type=AIAgentDependencies,
            output_type=Union[CodingResponse, ErrorResponse],
            system_prompt=self._get_coding_system_prompt(),
            retries=3
        )
        
        # Add dynamic system prompt for context
        @agent.system_prompt
        async def add_context(ctx: RunContext[AIAgentDependencies]) -> str:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            return f"""
            Current session: {ctx.deps.session_id or 'anonymous'}
            Current time: {current_time}
            User preferences: Advanced developer seeking comprehensive, production-ready solutions
            """
        
        # Add tools for enhanced functionality
        @agent.tool
        async def search_documentation(
            ctx: RunContext[AIAgentDependencies], 
            query: str, 
            language: Optional[str] = None
        ) -> str:
            """Search for relevant documentation and examples"""
            try:
                # Simulate documentation search (in production, integrate with real APIs)
                search_url = f"https://api.github.com/search/repositories"
                params = {
                    "q": f"{query} language:{language}" if language else query,
                    "sort": "stars",
                    "order": "desc",
                    "per_page": 3
                }
                
                response = await ctx.deps.http_client.get(search_url, params=params)
                if response.status_code == 200:
                    data = response.json()
                    results = []
                    for repo in data.get("items", [])[:3]:
                        results.append(f"- {repo['name']}: {repo['description']} (⭐{repo['stargazers_count']})")
                    return "Relevant repositories:\n" + "\n".join(results)
                else:
                    return "Documentation search temporarily unavailable"
            except Exception as e:
                logger.error(f"Documentation search failed: {e}")
                return "Unable to search documentation at this time"
        
        # Add output validator for quality assurance
        @agent.output_validator
        async def validate_coding_response(
            ctx: RunContext[AIAgentDependencies], 
            output: Union[CodingResponse, ErrorResponse]
        ) -> Union[CodingResponse, ErrorResponse]:
            """Validate and enhance the coding response"""
            if isinstance(output, ErrorResponse):
                return output
            
            # Ensure minimum quality standards
            if len(output.explanation) < 50:
                raise ModelRetry("Response too brief. Please provide a more comprehensive explanation.")
            
            # Enhance confidence scoring based on response completeness
            completeness_score = 0.0
            if output.code_snippet:
                completeness_score += 0.3
            if output.best_practices:
                completeness_score += 0.2
            if output.additional_resources:
                completeness_score += 0.2
            if len(output.explanation) > 200:
                completeness_score += 0.3
            
            # Adjust confidence score
            output.confidence_score = min(output.confidence_score + completeness_score, 1.0)
            
            return output
        
        return agent
    
    def _create_debug_agent(self) -> Agent:
        """Create specialized debugging agent"""
        return Agent(
            model=ModelProvider.DEEPSEEK_CHAT.value,
            deps_type=AIAgentDependencies,
            output_type=CodingResponse,
            system_prompt="""
            You are a specialized debugging expert. Focus on:
            1. Identifying root causes of issues
            2. Providing step-by-step debugging approaches
            3. Suggesting preventive measures
            4. Offering multiple solution paths
            Always include specific debugging techniques and tools.
            """,
            retries=2
        )
    
    def _create_architecture_agent(self) -> Agent:
        """Create specialized architecture advice agent"""
        return Agent(
            model=ModelProvider.DEEPSEEK_CHAT.value,
            deps_type=AIAgentDependencies,
            output_type=CodingResponse,
            system_prompt="""
            You are a senior software architect. Focus on:
            1. System design patterns and principles
            2. Scalability and performance considerations
            3. Security best practices
            4. Technology stack recommendations
            5. Code organization and structure
            Always consider long-term maintainability and team collaboration.
            """,
            retries=2
        )
    
    def _get_coding_system_prompt(self) -> str:
        """Get the comprehensive system prompt for the coding agent"""
        return """
        You are an expert AI coding assistant with deep knowledge across multiple programming languages,
        frameworks, and software engineering best practices. Your responses should be:
        
        1. **Comprehensive**: Provide thorough explanations with context
        2. **Practical**: Include working code examples when relevant
        3. **Educational**: Explain the 'why' behind recommendations
        4. **Current**: Use modern, up-to-date practices and patterns
        5. **Secure**: Always consider security implications
        6. **Performance-aware**: Mention performance considerations
        7. **Maintainable**: Emphasize clean, readable code
        
        For each response:
        - Identify the type of question (explanation, generation, debugging, etc.)
        - Detect the programming language if applicable
        - Provide clear, actionable advice
        - Include relevant best practices
        - Suggest follow-up questions or areas to explore
        - Rate your confidence in the response
        
        Always strive for production-ready, professional-grade solutions.
        """

    async def get_response(
        self,
        question: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Union[CodingResponse, ErrorResponse]:
        """
        Get AI response with intelligent routing and fallback handling
        """
        # Determine the best agent based on question type
        agent = self._select_agent(question)

        # Set usage limits to prevent runaway costs
        usage_limits = UsageLimits(
            response_tokens_limit=self.deps.max_tokens,
            request_limit=5  # Prevent infinite retry loops
        )

        # Try primary models first, then fallbacks
        models_to_try = self.primary_models + self.fallback_models

        for model_name in models_to_try:
            try:
                logger.info(f"Attempting request with model: {model_name}")

                # Override the agent's model for this request
                with agent.override(model=model_name.value):
                    result = await agent.run(
                        question,
                        deps=self.deps,
                        usage_limits=usage_limits
                    )

                    logger.info(f"Successful response from {model_name}")
                    return result.output

            except UsageLimitExceeded as e:
                logger.warning(f"Usage limit exceeded with {model_name}: {e}")
                return ErrorResponse(
                    error_type="usage_limit_exceeded",
                    message="Response would be too long. Please try a more specific question.",
                    suggestion="Break down your question into smaller, more focused parts."
                )

            except UnexpectedModelBehavior as e:
                logger.warning(f"Unexpected behavior from {model_name}: {e}")
                continue  # Try next model

            except ValidationError as e:
                logger.error(f"Validation error with {model_name}: {e}")
                continue  # Try next model

            except Exception as e:
                logger.error(f"Error with {model_name}: {e}")
                continue  # Try next model

        # If all models failed, return error response
        return ErrorResponse(
            error_type="all_models_failed",
            message="Unable to process your request at this time. Please try again later.",
            suggestion="Check your internet connection and try rephrasing your question.",
            retry_after=60
        )

    def _select_agent(self, question: str) -> Agent:
        """Select the most appropriate agent based on question content"""
        question_lower = question.lower()

        # Keywords for different agent types
        debug_keywords = ["error", "bug", "debug", "fix", "broken", "not working", "exception"]
        architecture_keywords = ["design", "architecture", "pattern", "structure", "organize", "scale"]

        if any(keyword in question_lower for keyword in debug_keywords):
            logger.info("Selected debug agent")
            return self.debug_agent
        elif any(keyword in question_lower for keyword in architecture_keywords):
            logger.info("Selected architecture agent")
            return self.architecture_agent
        else:
            logger.info("Selected general coding agent")
            return self.coding_agent

    async def get_response_sync(
        self,
        question: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Union[CodingResponse, ErrorResponse]:
        """Synchronous wrapper for async get_response method"""
        try:
            return await self.get_response(question, context)
        except Exception as e:
            logger.error(f"Sync wrapper error: {e}")
            return ErrorResponse(
                error_type="internal_error",
                message="An internal error occurred while processing your request.",
                suggestion="Please try again or contact support if the issue persists."
            )


class OpenRouterClient:
    """
    Direct OpenRouter API client with advanced error handling and retry logic
    """

    def __init__(self, api_key: str, base_url: str = "https://openrouter.ai/api/v1"):
        self.api_key = api_key
        self.base_url = base_url
        self.session = None

    async def __aenter__(self):
        self.session = httpx.AsyncClient(
            timeout=httpx.Timeout(30.0),
            limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.aclose()

    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = "openai/gpt-4o-mini",
        max_tokens: int = 1000,
        temperature: float = 0.7,
        **kwargs
    ) -> Dict[str, Any]:
        """Make a chat completion request with comprehensive error handling"""

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://ai-coding-assistant.local",
            "X-Title": "AI Coding Assistant"
        }

        payload = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            **kwargs
        }

        try:
            response = await self.session.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload
            )

            if response.status_code == 200:
                return response.json()
            elif response.status_code == 429:
                # Rate limited
                retry_after = int(response.headers.get("Retry-After", 60))
                raise Exception(f"Rate limited. Retry after {retry_after} seconds.")
            elif response.status_code == 402:
                # Insufficient credits
                raise Exception("Insufficient credits. Please add more credits to your OpenRouter account.")
            elif response.status_code >= 500:
                # Server error
                raise Exception(f"OpenRouter server error: {response.status_code}")
            else:
                # Other client errors
                error_data = response.json() if response.content else {}
                error_msg = error_data.get("error", {}).get("message", f"HTTP {response.status_code}")
                raise Exception(f"OpenRouter API error: {error_msg}")

        except httpx.TimeoutException:
            raise Exception("Request timed out. Please try again.")
        except httpx.NetworkError:
            raise Exception("Network error. Please check your connection.")
        except Exception as e:
            if "Rate limited" in str(e) or "credits" in str(e) or "server error" in str(e):
                raise  # Re-raise known errors
            raise Exception(f"Unexpected error: {str(e)}")


# Factory function for easy agent creation
async def create_ai_agent(api_key: str, **kwargs) -> AIAgentSystem:
    """Factory function to create a configured AI agent system"""

    # Create HTTP client for the agent
    http_client = httpx.AsyncClient(
        timeout=httpx.Timeout(30.0),
        limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)
    )

    # Create dependencies
    deps = AIAgentDependencies(
        api_key=api_key,
        http_client=http_client,
        **kwargs
    )

    return AIAgentSystem(deps)
