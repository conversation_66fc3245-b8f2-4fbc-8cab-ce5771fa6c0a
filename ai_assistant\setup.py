#!/usr/bin/env python3
"""
AI Coding Assistant - Comprehensive Setup Script
Handles installation, configuration, and deployment with style!
"""

import os
import sys
import subprocess
import shutil
import json
import secrets
from pathlib import Path
from typing import Optional

# Color codes for beautiful terminal output
class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def print_banner():
    """Print a beautiful banner"""
    banner = f"""
{Colors.HEADER}{Colors.BOLD}
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    🤖 AI CODING ASSISTANT - SETUP WIZARD 🚀                 ║
║                                                              ║
║    Production-ready AI assistant with modern web UI         ║
║    Built with Flask, Pydantic AI, and advanced features     ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
{Colors.ENDC}
"""
    print(banner)

def print_step(step_num: int, total_steps: int, message: str):
    """Print a formatted step message"""
    print(f"{Colors.OKCYAN}[{step_num}/{total_steps}]{Colors.ENDC} {Colors.BOLD}{message}{Colors.ENDC}")

def print_success(message: str):
    """Print a success message"""
    print(f"{Colors.OKGREEN}✅ {message}{Colors.ENDC}")

def print_warning(message: str):
    """Print a warning message"""
    print(f"{Colors.WARNING}⚠️  {message}{Colors.ENDC}")

def print_error(message: str):
    """Print an error message"""
    print(f"{Colors.FAIL}❌ {message}{Colors.ENDC}")

def print_info(message: str):
    """Print an info message"""
    print(f"{Colors.OKBLUE}ℹ️  {message}{Colors.ENDC}")

def run_command(command: str, description: str = None) -> bool:
    """Run a command and return success status"""
    if description:
        print(f"   {Colors.OKBLUE}Running: {description}{Colors.ENDC}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        return True
    except subprocess.CalledProcessError as e:
        print_error(f"Command failed: {command}")
        print_error(f"Error: {e.stderr}")
        return False

def check_python_version() -> bool:
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print_error(f"Python 3.9+ required. Current version: {version.major}.{version.minor}")
        return False
    
    print_success(f"Python {version.major}.{version.minor}.{version.micro} ✓")
    return True

def create_virtual_environment() -> bool:
    """Create and activate virtual environment"""
    venv_path = Path("venv")
    
    if venv_path.exists():
        print_info("Virtual environment already exists")
        return True
    
    print_info("Creating virtual environment...")
    return run_command(f"{sys.executable} -m venv venv", "Creating virtual environment")

def install_dependencies() -> bool:
    """Install required dependencies"""
    print_info("Installing dependencies...")
    
    # Determine the correct pip path
    if os.name == 'nt':  # Windows
        pip_path = "venv\\Scripts\\pip"
    else:  # Unix/Linux/macOS
        pip_path = "venv/bin/pip"
    
    # Install requirements
    commands = [
        f"{pip_path} install --upgrade pip",
        f"{pip_path} install flask pydantic python-dotenv httpx pydantic-ai",
        f"{pip_path} install flask-cors werkzeug",
        f"{pip_path} install gunicorn uvicorn"  # Production servers
    ]
    
    for command in commands:
        if not run_command(command):
            return False
    
    return True

def setup_environment_file() -> bool:
    """Setup .env file with configuration"""
    env_file = Path(".env")
    
    if env_file.exists():
        print_info(".env file already exists")
        return True
    
    print_info("Creating .env configuration file...")
    
    # Generate a secure secret key
    secret_key = secrets.token_hex(32)
    
    env_content = f"""# AI Coding Assistant Configuration
# Generated by setup script on {os.popen('date').read().strip()}

# OpenRouter API Configuration
# Get your API key from: https://openrouter.ai/keys
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Flask Configuration
SECRET_KEY={secret_key}
FLASK_DEBUG=True
FLASK_TESTING=False

# Security and Rate Limiting
MAX_REQUESTS_PER_MINUTE=30
MAX_TOKENS_PER_REQUEST=4000
SESSION_TIMEOUT_MINUTES=60

# Server Configuration
PORT=5000
HOST=0.0.0.0

# Feature Flags
ENABLE_CONVERSATION_HISTORY=True
ENABLE_EXPORT_FEATURE=True
ENABLE_SETTINGS_MODAL=True
ENABLE_SOUND_NOTIFICATIONS=True

# AI Model Preferences
PRIMARY_MODELS=openai/gpt-4o,anthropic/claude-3-5-sonnet-latest,google/gemini-2.0-flash-exp
FALLBACK_MODELS=openai/gpt-4o-mini,anthropic/claude-3-5-haiku-latest,meta-llama/llama-3.3-70b-instruct
"""
    
    try:
        with open(env_file, 'w') as f:
            f.write(env_content)
        print_success("Environment file created")
        return True
    except Exception as e:
        print_error(f"Failed to create .env file: {e}")
        return False

def setup_directories() -> bool:
    """Create necessary directories"""
    directories = [
        "data",
        "logs",
        "static",
        "templates"
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            try:
                dir_path.mkdir(parents=True, exist_ok=True)
                print_success(f"Created directory: {directory}")
            except Exception as e:
                print_error(f"Failed to create directory {directory}: {e}")
                return False
    
    return True

def create_startup_scripts() -> bool:
    """Create convenient startup scripts"""
    
    # Windows batch file
    windows_script = """@echo off
echo Starting AI Coding Assistant...
cd /d "%~dp0"
call venv\\Scripts\\activate
python demo.py
pause
"""
    
    # Unix shell script
    unix_script = """#!/bin/bash
echo "Starting AI Coding Assistant..."
cd "$(dirname "$0")"
source venv/bin/activate
python demo.py
"""
    
    try:
        # Create Windows script
        with open("start_demo.bat", 'w') as f:
            f.write(windows_script)
        
        # Create Unix script
        with open("start_demo.sh", 'w') as f:
            f.write(unix_script)
        
        # Make Unix script executable
        if os.name != 'nt':
            os.chmod("start_demo.sh", 0o755)
        
        print_success("Startup scripts created")
        return True
    except Exception as e:
        print_error(f"Failed to create startup scripts: {e}")
        return False

def run_tests() -> bool:
    """Run basic tests to ensure everything works"""
    print_info("Running basic tests...")
    
    try:
        # Test imports
        import flask
        import pydantic
        import httpx
        print_success("All dependencies imported successfully")
        
        # Test demo app
        from demo import create_app
        app = create_app()
        print_success("Demo app created successfully")
        
        return True
    except Exception as e:
        print_error(f"Tests failed: {e}")
        return False

def print_completion_message():
    """Print completion message with instructions"""
    message = f"""
{Colors.OKGREEN}{Colors.BOLD}
🎉 SETUP COMPLETE! 🎉
{Colors.ENDC}

{Colors.HEADER}Your AI Coding Assistant is ready to use!{Colors.ENDC}

{Colors.BOLD}Quick Start:{Colors.ENDC}
1. {Colors.OKCYAN}Demo Mode (No API key needed):{Colors.ENDC}
   • Windows: Double-click {Colors.BOLD}start_demo.bat{Colors.ENDC}
   • Unix/Mac: Run {Colors.BOLD}./start_demo.sh{Colors.ENDC}
   • Manual: {Colors.BOLD}python demo.py{Colors.ENDC}

2. {Colors.OKCYAN}Full Version (Requires OpenRouter API key):{Colors.ENDC}
   • Get API key: {Colors.UNDERLINE}https://openrouter.ai/keys{Colors.ENDC}
   • Edit {Colors.BOLD}.env{Colors.ENDC} file and add your API key
   • Run: {Colors.BOLD}python run.py{Colors.ENDC}

{Colors.BOLD}Features:{Colors.ENDC}
✅ Modern, responsive web interface
✅ Advanced AI with multiple model support
✅ Real-time conversation history
✅ Offline support with service worker
✅ Export conversations
✅ Dark/light theme switching
✅ Accessibility features
✅ Production-ready architecture

{Colors.BOLD}URLs:{Colors.ENDC}
• Main App: {Colors.UNDERLINE}http://localhost:5000{Colors.ENDC}
• Health Check: {Colors.UNDERLINE}http://localhost:5000/health{Colors.ENDC}
• API Docs: See README.md

{Colors.WARNING}Need help? Check the README.md file for detailed documentation!{Colors.ENDC}

{Colors.OKGREEN}Happy coding! 🚀{Colors.ENDC}
"""
    print(message)

def main():
    """Main setup function"""
    print_banner()
    
    total_steps = 8
    current_step = 0
    
    # Step 1: Check Python version
    current_step += 1
    print_step(current_step, total_steps, "Checking Python version")
    if not check_python_version():
        sys.exit(1)
    
    # Step 2: Create virtual environment
    current_step += 1
    print_step(current_step, total_steps, "Setting up virtual environment")
    if not create_virtual_environment():
        print_error("Failed to create virtual environment")
        sys.exit(1)
    
    # Step 3: Install dependencies
    current_step += 1
    print_step(current_step, total_steps, "Installing dependencies")
    if not install_dependencies():
        print_error("Failed to install dependencies")
        sys.exit(1)
    
    # Step 4: Setup environment file
    current_step += 1
    print_step(current_step, total_steps, "Configuring environment")
    if not setup_environment_file():
        print_error("Failed to setup environment")
        sys.exit(1)
    
    # Step 5: Create directories
    current_step += 1
    print_step(current_step, total_steps, "Creating directories")
    if not setup_directories():
        print_error("Failed to create directories")
        sys.exit(1)
    
    # Step 6: Create startup scripts
    current_step += 1
    print_step(current_step, total_steps, "Creating startup scripts")
    if not create_startup_scripts():
        print_error("Failed to create startup scripts")
        sys.exit(1)
    
    # Step 7: Run tests
    current_step += 1
    print_step(current_step, total_steps, "Running tests")
    if not run_tests():
        print_error("Tests failed")
        sys.exit(1)
    
    # Step 8: Complete
    current_step += 1
    print_step(current_step, total_steps, "Finalizing setup")
    
    print_completion_message()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print_error("\nSetup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_error(f"Setup failed with error: {e}")
        sys.exit(1)
