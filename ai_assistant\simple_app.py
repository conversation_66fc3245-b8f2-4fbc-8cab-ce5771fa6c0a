#!/usr/bin/env python3
"""
AI Coding Assistant - Simplified Version for DeepSeek
Direct OpenRouter integration without Pydantic AI complexity
"""

import json
import os
import uuid
import requests
from datetime import datetime
from pathlib import Path

from flask import Flask, jsonify, render_template, request, session
from pydantic import BaseModel, ValidationError
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class QuestionRequest(BaseModel):
    question: str
    context: dict = {}
    session_id: str = None

class AnswerResponse(BaseModel):
    answer: dict
    session_id: str
    timestamp: str
    processing_time_ms: int
    model_used: str = None

def create_app():
    app = Flask(__name__)
    app.secret_key = os.getenv('SECRET_KEY', 'dev-secret-key')
    
    # Ensure data directory exists
    Path('data').mkdir(exist_ok=True)
    
    @app.route('/')
    def index():
        if 'session_id' not in session:
            session['session_id'] = str(uuid.uuid4())
        return render_template('index.html', session_id=session['session_id'])
    
    @app.route('/ask', methods=['POST'])
    def ask_ai():
        start_time = datetime.now()
        
        try:
            # Validate request
            data = request.get_json()
            if not data:
                return jsonify({"error": "No JSON data provided"}), 400
                
            question_req = QuestionRequest(**data)
            
            # Get or create session ID
            session_id = question_req.session_id or session.get('session_id', str(uuid.uuid4()))
            if 'session_id' not in session:
                session['session_id'] = session_id
            
            # Get OpenRouter API key
            api_key = os.getenv('OPENROUTER_API_KEY')
            if not api_key or api_key == 'your_openrouter_api_key_here':
                return jsonify({
                    "error": "api_key_missing",
                    "message": "OpenRouter API key not configured. Please set OPENROUTER_API_KEY in your .env file.",
                    "timestamp": datetime.now().isoformat()
                }), 400
            
            # Call OpenRouter API directly
            ai_response = call_openrouter_api(question_req.question, api_key)
            
            if ai_response.get('error'):
                return jsonify({
                    "error": "ai_api_error",
                    "message": ai_response['error'],
                    "timestamp": datetime.now().isoformat()
                }), 500
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            
            # Format response
            formatted_response = {
                "explanation": ai_response.get('content', 'No response received'),
                "response_type": "general_programming",
                "language": detect_language(question_req.question),
                "confidence_score": 0.85,
                "best_practices": extract_best_practices(ai_response.get('content', '')),
                "follow_up_suggestions": [
                    "Can you explain this in more detail?",
                    "What are the potential issues with this approach?",
                    "Are there alternative solutions?"
                ]
            }
            
            # Create response
            response = AnswerResponse(
                answer=formatted_response,
                session_id=session_id,
                timestamp=start_time.isoformat(),
                processing_time_ms=int(processing_time),
                model_used="deepseek/deepseek-chat-v3-0324:free"
            )
            
            # Save to history
            save_to_history(
                session_id=session_id,
                question=question_req.question,
                answer=formatted_response['explanation'],
                response_data=formatted_response,
                processing_time_ms=int(processing_time)
            )
            
            return jsonify(response.model_dump())
            
        except ValidationError as e:
            return jsonify({
                "error": "validation_error",
                "message": "Invalid request data",
                "details": e.errors(),
                "timestamp": datetime.now().isoformat()
            }), 400
        except Exception as e:
            print(f"Error processing request: {e}")
            return jsonify({
                "error": "internal_error",
                "message": f"An error occurred: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }), 500
    
    @app.route('/history')
    def get_history():
        session_id = session.get('session_id')
        if not session_id:
            return jsonify({"history": []})
        
        try:
            history = load_history()
            session_history = [
                entry for entry in history
                if entry.get('session_id') == session_id
            ]
            session_history.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
            return jsonify({"history": session_history[:50]})
        except Exception:
            return jsonify({"history": []})
    
    @app.route('/health')
    def health_check():
        api_key = os.getenv('OPENROUTER_API_KEY')
        api_configured = api_key and api_key != 'your_openrouter_api_key_here'
        
        return jsonify({
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0-simple",
            "api_configured": api_configured,
            "model": "deepseek/deepseek-chat-v3-0324:free"
        })
    
    return app

def call_openrouter_api(question: str, api_key: str) -> dict:
    """Call OpenRouter API directly"""
    
    url = "https://openrouter.ai/api/v1/chat/completions"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "HTTP-Referer": "http://localhost:5000",
        "X-Title": "AI Coding Assistant"
    }
    
    # Enhanced system prompt for coding assistance
    system_prompt = """You are an expert AI coding assistant. Provide comprehensive, accurate answers to programming questions. Include:

1. Clear explanations with context
2. Working code examples when relevant
3. Best practices and recommendations
4. Potential pitfalls to avoid
5. Alternative approaches when applicable

Format your response to be helpful for developers of all skill levels."""
    
    payload = {
        "model": "deepseek/deepseek-chat-v3-0324:free",
        "messages": [
            {
                "role": "system",
                "content": system_prompt
            },
            {
                "role": "user",
                "content": question
            }
        ],
        "max_tokens": 2000,
        "temperature": 0.7,
        "top_p": 0.9,
        "frequency_penalty": 0.1,
        "presence_penalty": 0.1
    }
    
    try:
        print(f"Calling OpenRouter API with model: deepseek/deepseek-chat-v3-0324:free")
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        
        print(f"API Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if 'choices' in data and len(data['choices']) > 0:
                content = data['choices'][0]['message']['content']
                return {"content": content, "usage": data.get('usage', {})}
            else:
                return {"error": "No response content received from API"}
        elif response.status_code == 401:
            return {"error": "Invalid API key. Please check your OpenRouter API key."}
        elif response.status_code == 402:
            return {"error": "Insufficient credits. Please add credits to your OpenRouter account."}
        elif response.status_code == 429:
            return {"error": "Rate limit exceeded. Please try again in a moment."}
        else:
            error_text = response.text
            print(f"API Error Response: {error_text}")
            return {"error": f"API error {response.status_code}: {error_text}"}
            
    except requests.exceptions.Timeout:
        return {"error": "Request timed out. Please try again."}
    except requests.exceptions.ConnectionError:
        return {"error": "Connection error. Please check your internet connection."}
    except Exception as e:
        print(f"Unexpected error calling API: {e}")
        return {"error": f"Unexpected error: {str(e)}"}

def detect_language(question: str) -> str:
    """Detect programming language from question"""
    question_lower = question.lower()
    
    languages = {
        'python': ['python', 'django', 'flask', 'pandas', 'numpy'],
        'javascript': ['javascript', 'js', 'node', 'react', 'vue', 'angular'],
        'java': ['java', 'spring', 'maven', 'gradle'],
        'csharp': ['c#', 'csharp', '.net', 'asp.net'],
        'cpp': ['c++', 'cpp'],
        'rust': ['rust', 'cargo'],
        'go': ['golang', 'go'],
        'php': ['php', 'laravel', 'symfony']
    }
    
    for lang, keywords in languages.items():
        if any(keyword in question_lower for keyword in keywords):
            return lang
    
    return 'general'

def extract_best_practices(content: str) -> list:
    """Extract best practices from AI response"""
    practices = []
    
    # Look for common best practice indicators
    lines = content.split('\n')
    for line in lines:
        line_lower = line.lower().strip()
        if any(indicator in line_lower for indicator in [
            'best practice', 'recommendation', 'should', 'avoid', 'remember',
            'important', 'tip:', 'note:', 'warning:'
        ]):
            if len(line.strip()) > 10:  # Avoid very short lines
                practices.append(line.strip())
    
    # If no specific practices found, add some general ones
    if not practices:
        practices = [
            "Follow consistent code formatting and style guidelines",
            "Write clear, descriptive variable and function names",
            "Add appropriate comments and documentation",
            "Handle errors gracefully with proper exception handling",
            "Test your code thoroughly before deployment"
        ]
    
    return practices[:5]  # Limit to 5 practices

def save_to_history(session_id: str, question: str, answer: str, response_data: dict = None, processing_time_ms: int = 0):
    """Save conversation entry to history"""
    try:
        history = load_history()
        entry = {
            "id": str(uuid.uuid4()),
            "session_id": session_id,
            "timestamp": datetime.now().isoformat(),
            "question": question,
            "answer": answer,
            "error": False,
            "processing_time_ms": processing_time_ms,
            "response_data": response_data
        }
        history.append(entry)
        
        # Keep only recent entries
        if len(history) > 100:
            history = history[-100:]
        
        with open('data/history.json', 'w', encoding='utf-8') as f:
            json.dump(history, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"Failed to save history: {e}")

def load_history() -> list:
    """Load conversation history from file"""
    try:
        if Path('data/history.json').exists():
            with open('data/history.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        return []
    except Exception as e:
        print(f"Failed to load history: {e}")
        return []

if __name__ == '__main__':
    print("🚀 Starting AI Coding Assistant (Simple Version)")
    print("🤖 Using DeepSeek Chat v3 model")
    
    # Check API key
    api_key = os.getenv('OPENROUTER_API_KEY')
    if not api_key or api_key == 'your_openrouter_api_key_here':
        print("❌ OpenRouter API key not configured!")
        print("📝 Please set OPENROUTER_API_KEY in your .env file")
        print("🔗 Get your API key from: https://openrouter.ai/keys")
    else:
        print("✅ API key configured")
    
    print("🌐 Server starting on http://localhost:5000")
    print("⏹️  Press Ctrl+C to stop the server")
    
    app = create_app()
    app.run(host='0.0.0.0', port=5000, debug=True)
