#!/usr/bin/env python3
"""
AI Coding Assistant - Comprehensive Test Suite
Tests all components, API endpoints, and functionality
"""

import asyncio
import json
import os
import sys
import time
import unittest
from pathlib import Path
from unittest.mock import Mock, patch
import requests
import threading
from datetime import datetime

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

try:
    from demo import create_app as create_demo_app
    # Set dummy environment variable for testing
    os.environ.setdefault('OPENROUTER_API_KEY', 'test-key-for-testing')
    from agents import AIAgentSystem, AIAgentDependencies
    import httpx
    from pydantic import ValidationError
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please install requirements: pip install -r requirements.txt")
    sys.exit(1)

class Colors:
    """Terminal colors for beautiful output"""
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_test_result(test_name: str, success: bool, message: str = ""):
    """Print formatted test result"""
    status = f"{Colors.OKGREEN}✅ PASS{Colors.ENDC}" if success else f"{Colors.FAIL}❌ FAIL{Colors.ENDC}"
    print(f"{status} {Colors.BOLD}{test_name}{Colors.ENDC} {message}")

class TestDemoApp(unittest.TestCase):
    """Test the demo application"""
    
    def setUp(self):
        """Set up test client"""
        self.app = create_demo_app()
        self.app.config['TESTING'] = True
        self.client = self.app.test_client()
        self.ctx = self.app.app_context()
        self.ctx.push()
    
    def tearDown(self):
        """Clean up"""
        self.ctx.pop()
    
    def test_home_page(self):
        """Test home page loads correctly"""
        response = self.client.get('/')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'AI Coding Assistant', response.data)
        print_test_result("Home page loads", True)
    
    def test_health_check(self):
        """Test health check endpoint"""
        response = self.client.get('/health')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'healthy')
        self.assertIn('demo', data['mode'])
        print_test_result("Health check endpoint", True)
    
    def test_ask_endpoint_valid_request(self):
        """Test ask endpoint with valid request"""
        payload = {
            "question": "How do I implement authentication in Flask?",
            "session_id": "test-session"
        }
        response = self.client.post('/ask', 
                                  data=json.dumps(payload),
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertIn('answer', data)
        self.assertIn('session_id', data)
        self.assertIn('timestamp', data)
        print_test_result("Ask endpoint with valid request", True)
    
    def test_ask_endpoint_invalid_request(self):
        """Test ask endpoint with invalid request"""
        payload = {}  # Missing required 'question' field
        response = self.client.post('/ask',
                                  data=json.dumps(payload),
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('error', data)
        print_test_result("Ask endpoint with invalid request", True)
    
    def test_history_endpoint(self):
        """Test history endpoint"""
        # First make a request to create history
        payload = {"question": "Test question"}
        self.client.post('/ask',
                        data=json.dumps(payload),
                        content_type='application/json')
        
        # Then check history
        response = self.client.get('/history')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertIn('history', data)
        print_test_result("History endpoint", True)

class TestAIAgents(unittest.TestCase):
    """Test AI agent system"""
    
    def setUp(self):
        """Set up mock dependencies"""
        self.mock_client = Mock(spec=httpx.AsyncClient)
        self.deps = AIAgentDependencies(
            api_key="test-key",
            http_client=self.mock_client,
            rate_limit_per_minute=60,
            max_tokens=1000
        )
    
    def test_agent_initialization(self):
        """Test AI agent system initialization"""
        try:
            agent_system = AIAgentSystem(self.deps)
            self.assertIsNotNone(agent_system)
            self.assertIsNotNone(agent_system.coding_agent)
            self.assertIsNotNone(agent_system.debug_agent)
            self.assertIsNotNone(agent_system.architecture_agent)
            print_test_result("AI agent initialization", True)
        except Exception as e:
            print_test_result("AI agent initialization", False, str(e))
            self.fail(f"Agent initialization failed: {e}")
    
    def test_agent_selection(self):
        """Test intelligent agent selection"""
        agent_system = AIAgentSystem(self.deps)
        
        # Test debug agent selection
        debug_question = "I'm getting an error in my code"
        selected_agent = agent_system._select_agent(debug_question)
        self.assertEqual(selected_agent, agent_system.debug_agent)
        
        # Test architecture agent selection
        arch_question = "How should I design my application architecture?"
        selected_agent = agent_system._select_agent(arch_question)
        self.assertEqual(selected_agent, agent_system.architecture_agent)
        
        # Test general coding agent selection
        general_question = "How do I use Python lists?"
        selected_agent = agent_system._select_agent(general_question)
        self.assertEqual(selected_agent, agent_system.coding_agent)
        
        print_test_result("Agent selection logic", True)

class TestAPIPerformance(unittest.TestCase):
    """Test API performance and load handling"""
    
    def setUp(self):
        """Set up test server"""
        self.app = create_demo_app()
        self.app.config['TESTING'] = True
        self.client = self.app.test_client()
    
    def test_response_time(self):
        """Test API response time"""
        start_time = time.time()
        response = self.client.get('/health')
        end_time = time.time()
        
        response_time = (end_time - start_time) * 1000  # Convert to milliseconds
        self.assertLess(response_time, 1000)  # Should respond within 1 second
        print_test_result("API response time", True, f"({response_time:.2f}ms)")
    
    def test_concurrent_requests(self):
        """Test handling of concurrent requests"""
        def make_request():
            return self.client.get('/health')
        
        # Create multiple threads to simulate concurrent requests
        threads = []
        results = []
        
        for _ in range(10):
            thread = threading.Thread(target=lambda: results.append(make_request()))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Check all requests succeeded
        success_count = sum(1 for result in results if result and result.status_code == 200)
        self.assertEqual(success_count, 10)
        print_test_result("Concurrent requests handling", True, f"({success_count}/10 successful)")

class TestSecurity(unittest.TestCase):
    """Test security features"""
    
    def setUp(self):
        """Set up test client"""
        self.app = create_demo_app()
        self.app.config['TESTING'] = True
        self.client = self.app.test_client()
    
    def test_xss_protection(self):
        """Test XSS protection"""
        malicious_payload = {
            "question": "<script>alert('xss')</script>"
        }
        response = self.client.post('/ask',
                                  data=json.dumps(malicious_payload),
                                  content_type='application/json')
        
        # Should not contain unescaped script tags in response
        self.assertNotIn(b'<script>', response.data)
        print_test_result("XSS protection", True)
    
    def test_sql_injection_protection(self):
        """Test SQL injection protection"""
        malicious_payload = {
            "question": "'; DROP TABLE users; --"
        }
        response = self.client.post('/ask',
                                  data=json.dumps(malicious_payload),
                                  content_type='application/json')
        
        # Should handle malicious input gracefully
        self.assertIn([200, 400], [response.status_code])
        print_test_result("SQL injection protection", True)
    
    def test_rate_limiting_simulation(self):
        """Test rate limiting (simulation)"""
        # Make multiple rapid requests
        responses = []
        for _ in range(5):
            response = self.client.post('/ask',
                                      data=json.dumps({"question": "test"}),
                                      content_type='application/json')
            responses.append(response.status_code)
        
        # All should succeed in demo mode (no actual rate limiting)
        success_count = sum(1 for status in responses if status == 200)
        self.assertGreater(success_count, 0)
        print_test_result("Rate limiting simulation", True, f"({success_count}/5 requests)")

class TestDataValidation(unittest.TestCase):
    """Test data validation with Pydantic"""
    
    def setUp(self):
        """Set up test client"""
        self.app = create_demo_app()
        self.app.config['TESTING'] = True
        self.client = self.app.test_client()
    
    def test_valid_json_request(self):
        """Test valid JSON request validation"""
        valid_payload = {
            "question": "How do I use Python?",
            "context": {"language": "python"},
            "session_id": "test-session-123"
        }
        response = self.client.post('/ask',
                                  data=json.dumps(valid_payload),
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 200)
        print_test_result("Valid JSON request validation", True)
    
    def test_invalid_json_request(self):
        """Test invalid JSON request handling"""
        response = self.client.post('/ask',
                                  data="invalid json",
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 400)
        print_test_result("Invalid JSON request handling", True)
    
    def test_missing_required_fields(self):
        """Test missing required fields validation"""
        invalid_payload = {
            "context": {"test": "value"}
            # Missing required 'question' field
        }
        response = self.client.post('/ask',
                                  data=json.dumps(invalid_payload),
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('error', data)
        print_test_result("Missing required fields validation", True)

def run_integration_tests():
    """Run integration tests with live server"""
    print(f"\n{Colors.BOLD}🧪 Running Integration Tests{Colors.ENDC}")
    
    # Start demo server in background
    import subprocess
    import time
    
    try:
        # Start server
        server_process = subprocess.Popen([
            sys.executable, "demo.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for server to start
        time.sleep(3)
        
        # Test live endpoints
        try:
            response = requests.get("http://localhost:5000/health", timeout=5)
            if response.status_code == 200:
                print_test_result("Live server health check", True)
            else:
                print_test_result("Live server health check", False, f"Status: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print_test_result("Live server health check", False, str(e))
        
        # Test live API
        try:
            payload = {"question": "Test integration question"}
            response = requests.post("http://localhost:5000/ask", 
                                   json=payload, timeout=10)
            if response.status_code == 200:
                print_test_result("Live API integration", True)
            else:
                print_test_result("Live API integration", False, f"Status: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print_test_result("Live API integration", False, str(e))
        
    finally:
        # Clean up server process
        if 'server_process' in locals():
            server_process.terminate()
            server_process.wait()

def main():
    """Run all tests"""
    print(f"{Colors.BOLD}🚀 AI Coding Assistant - Comprehensive Test Suite{Colors.ENDC}")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestDemoApp,
        TestAIAgents,
        TestAPIPerformance,
        TestSecurity,
        TestDataValidation
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=0, stream=open(os.devnull, 'w'))
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 60)
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    successes = total_tests - failures - errors
    
    print(f"{Colors.BOLD}Test Summary:{Colors.ENDC}")
    print(f"  Total Tests: {total_tests}")
    print(f"  {Colors.OKGREEN}Passed: {successes}{Colors.ENDC}")
    print(f"  {Colors.FAIL}Failed: {failures}{Colors.ENDC}")
    print(f"  {Colors.WARNING}Errors: {errors}{Colors.ENDC}")
    
    if failures > 0 or errors > 0:
        print(f"\n{Colors.FAIL}❌ Some tests failed. Check the output above for details.{Colors.ENDC}")
        return False
    else:
        print(f"\n{Colors.OKGREEN}✅ All tests passed successfully!{Colors.ENDC}")
        
        # Run integration tests if unit tests pass
        run_integration_tests()
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
